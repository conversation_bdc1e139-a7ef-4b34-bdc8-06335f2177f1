FROM eclipse-temurin:17-jre-alpine

WORKDIR /app

COPY target/admin-api.jar admin-api.jar

COPY libs/opentelemetry-javaagent.jar /app/libs/

COPY app-config/logback-spring.xml /app/app-config/

EXPOSE 6010

# <PERSON><PERSON><PERSON> hình command để chạy ứng dụng
ENTRYPOINT ["java", "--add-opens", "java.base/java.math=ALL-UNNAMED", "--add-opens", "java.base/java.time=ALL-UNNAMED", "-jar", "admin-api.jar"]