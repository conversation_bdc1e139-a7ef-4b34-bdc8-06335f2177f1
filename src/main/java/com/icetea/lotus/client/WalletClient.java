package com.icetea.lotus.client;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.AdminHistoryWithdrawResponse;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.util.MessageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${feign.client.name}", url = "${feign.client.wallet.url}")
public interface WalletClient {

    @PostMapping("/wallet/api/v1/wallets/history/withdraw")
    Page<AdminHistoryWithdrawResponse> getAllWithdrawHistory(@RequestBody AdminHistoryWithdrawRequest request);

    @PostMapping("/wallet/api/v1/wallets/transfer/page-query")
    MessageResult getTransferHistory(@RequestBody TransferRecordDTO screen);
}
