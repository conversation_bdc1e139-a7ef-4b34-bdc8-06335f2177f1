package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.AutomainSetPassword;
import com.icetea.lotus.entity.spot.Automainconfig;
import com.icetea.lotus.entity.spot.MessageEncrypt;
import com.icetea.lotus.util.MessageResult;


public interface ExtendedAutomainconfigService {
    MessageResult coinList();

    MessageResult protocolList();

    MessageResult pageQuery(PageModel pageModel);

    MessageResult merge(Automainconfig automainconfig);

    MessageResult collectCoin(Automainconfig automainconfig);

    MessageResult setPassword(AutomainSetPassword automainconfig);

    MessageResult updateContract(AutomainSetPassword automainconfig);

    MessageResult encrypt(MessageEncrypt messageEncrypt) throws Exception;
}
