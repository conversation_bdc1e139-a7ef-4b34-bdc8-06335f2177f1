//package com.icetea.lotus.service.system;
//
//import com.icetea.lotus.constant.PageModel;
//import com.icetea.lotus.entity.spot.Admin;
//import com.icetea.lotus.util.MessageResult;
//import jakarta.servlet.http.HttpServletRequest;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.concurrent.CompletableFuture;
//
//
//public interface EmployeeService {
//
//    MessageResult adminLogin(String username, String password, String captcha);
//
//    MessageResult doLogin(String username, String password, String phone, String code, boolean rememberMe, HttpServletRequest request);
//
//    CompletableFuture<MessageResult> sendEmailMsg(String email, String msg, String subject);
//
//    MessageResult valiatePhoneCode(HttpServletRequest request);
//
//    MessageResult logout(RestTemplate restTemplate, String refreshToken);
//
//    MessageResult addAdmin(Admin admin, Long departmentId);
//
//    MessageResult findAllAdminUser(PageModel pageModel, String searchKey);
//
//    MessageResult updatePassword(Long id, String lastPassword, String newPassword);
//
//    MessageResult resetPassword(Long id);
//
//    MessageResult adminDetail(Long id);
//
//    MessageResult deletes(Long[] ids);
//
//    MessageResult refreshToken(String refreshToken);
//}
