package com.icetea.lotus.service.system;


import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedSysRoleService {
    MessageResult mergeRole(SysRole sysRole);

    MessageResult allMenu();

    MessageResult roleAllPermission(Long roleId);

    MessageResult getAllRole(PageModel pageModel);

    MessageResult deletes(Long id);
}
