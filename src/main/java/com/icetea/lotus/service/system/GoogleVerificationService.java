package com.icetea.lotus.service.system;

import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.util.MessageResult;

public interface GoogleVerificationService {

    MessageResult yzgoogle(AuthMember user, String codes);

    MessageResult sendgoogle(AuthMember user);

    MessageResult jcgoogle(String codes, AuthMember user, String password);

    MessageResult googleAuth(String codes, AuthMember user, String secret);
}
