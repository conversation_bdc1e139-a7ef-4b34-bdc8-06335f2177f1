package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.AnnouncementClassification;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedAnnouncementService {
    MessageResult create(String title, String content, String lang, AnnouncementClassification announcementClassification, Boolean isShow, String imgUrl);

    MessageResult toTop(long id);

    MessageResult toDown(long id);

    MessageResult page(PageModel pageModel, Boolean isShow);

    MessageResult deleteOne(Long[] ids);

    MessageResult detail(Long id);

    MessageResult turnOn(Long id);

    MessageResult turnOff(Long id);

    MessageResult update(Long id, String title, String content, Boolean isShow, String lang, AnnouncementClassification announcementClassification, String imgUrl);
}
