package com.icetea.lotus.service.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.model.screen.MemberDepositScreen;
import com.icetea.lotus.model.screen.MemberTransactionScreen;
import com.icetea.lotus.model.vo.MemberTransaction2ESVO;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Date;

public interface MemberFinanceService {
    MessageResult page(PageModel pageModel, MemberDepositScreen screen);

    MessageResult pageSuperDeposit(PageModel pageModel, MemberDepositScreen screen, Long memberId);

    MessageResult all();

    MessageResult detail(Long id);

    MessageResult pageQuery(PageModel pageModel, MemberTransactionScreen screen, HttpServletResponse response) throws IOException;

    MessageResult outExcel(Date startTime, Date endTime, TransactionType type, Long memberId, HttpServletRequest request, HttpServletResponse response) throws Exception;

    MessageResult getPageQueryByES(MemberTransaction2ESVO transactionVO);

    MessageResult pageQuerySuper(PageModel pageModel, MemberTransactionScreen screen, Long memberId);
}
