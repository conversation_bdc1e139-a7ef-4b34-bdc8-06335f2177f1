package com.icetea.lotus.service.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionTypeEnum;
import com.icetea.lotus.model.screen.ExchangeTradeScreen;
import com.icetea.lotus.util.MessageResult;

import java.util.Date;

public interface FinanceStatisticsService {
    MessageResult page(PageModel pageModel, ExchangeTradeScreen screen);

    MessageResult getResult(String[] types, Date startDate, Date endDate, String unit);

    MessageResult getFee(TransactionTypeEnum type, Date startDate, Date endDate, String unit);

    MessageResult recharge(TransactionTypeEnum type, Date startDate, Date endDate);
}
