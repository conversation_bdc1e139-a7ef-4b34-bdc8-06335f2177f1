package com.icetea.lotus.service.otc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.OtcCoin;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;

public interface AdminOtcCoinService {
    MessageResult create(OtcCoin otcCoin);

    MessageResult all();

    MessageResult detail(Long id);

    MessageResult update(OtcCoin otcCoin);

    MessageResult deletes(Long[] ids);

    MessageResult memberStatistics(Long id, BigDecimal jyRate);

    MessageResult pageQuery(PageModel pageModel);

    MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception; //NOSONAR

    MessageResult getAllOtcCoinUnits();
}
