package com.icetea.lotus.service.otc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.exception.InformationExpiredException;
import com.icetea.lotus.model.screen.AppealScreen;
import com.icetea.lotus.util.MessageResult;

public interface AdminAppealService {
    MessageResult pageQuery(PageModel pageModel, AppealScreen screen);

    MessageResult detail(Long id);

    MessageResult cancelOrder(long appealId, String orderSn, boolean banned) throws InformationExpiredException;

    MessageResult confirmRelease(long appealId, String orderSn, boolean banned) throws InformationExpiredException;
}
