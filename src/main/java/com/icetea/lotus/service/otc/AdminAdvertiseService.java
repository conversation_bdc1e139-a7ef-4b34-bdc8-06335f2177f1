package com.icetea.lotus.service.otc;

import com.icetea.lotus.constant.AdvertiseControlStatus;
import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.AdvertiseScreen;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Date;
import java.util.List;

public interface AdminAdvertiseService {
    MessageResult detail(Long id);

    MessageResult statue(Long[] ids, AdvertiseControlStatus status);

    MessageResult page(PageModel pageModel, AdvertiseScreen screen);

    MessageResult outExcel(Date startTime, Date endTime, AdvertiseType advertiseType, String realName, HttpServletRequest request, HttpServletResponse response) throws Exception;

    List<BooleanExpression> getBooleanExpressionList(Date startTime, Date endTime, AdvertiseType advertiseType, String realName);
}
