package com.icetea.lotus.service.otc;

import com.icetea.lotus.constant.OrderStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.OrderScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public interface AdminOrderService {
    MessageResult all();

    MessageResult detail(Long id);

    MessageResult status(Long id, OrderStatus status);

    MessageResult page(PageModel pageModel, OrderScreen screen);

    MessageResult getOrderNum();

    void outExcel(PageModel pageModel, OrderScreen screen, HttpServletResponse response) throws IOException;
}
