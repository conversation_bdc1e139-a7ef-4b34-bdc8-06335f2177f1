package com.icetea.lotus.service.redenvelope;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.RedEnvelope;
import com.icetea.lotus.util.MessageResult;

import java.math.BigDecimal;

public interface ExtendedRedEnvelopeService {
    MessageResult envelopeList(PageModel pageModel);

    MessageResult envelopeDetail(Long id);

    MessageResult envelopeDetailList(Long envelopeId, Integer pageNo, Integer pageSize);

    MessageResult addRedEnvelope(RedEnvelope redEnvelope);

    MessageResult modifyRedEnvelope(Long id, Integer type, Integer invite, String unit, BigDecimal maxRand, BigDecimal totalAmount, Integer count, String logoImage, String bgImage, String name, String detail, Integer expiredHours, Integer state);
}
