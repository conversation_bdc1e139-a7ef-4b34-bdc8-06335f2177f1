package com.icetea.lotus.service.index;

import com.icetea.lotus.util.MessageResult;

import java.util.Date;

public interface IndexService {
    MessageResult getYestodayStatisticsInfo(Date startDate, Date endDate);

    MessageResult getMemberStatisticsChart(Date startDate, Date endDate);

    MessageResult otcStatistics(Date startDate, Date endDate, String unit);

    MessageResult exchangeStatistics(Date startDate, Date endDate, String unit);

    MessageResult otcNumChart(Date startDate, Date endDate, String[] units);

    MessageResult exchangeNumStatistics(Date startDate, Date endDate, String baseSymbol, String[] coinSymbols);

    MessageResult affairs();

    MessageResult getAllExchangeCoin();
}
