package com.icetea.lotus.service.ctc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.util.MessageResult;

public interface AdminCtcOrderService {
    MessageResult orderList(PageModel pageModel);

    MessageResult orderDetail(Long id);

    MessageResult payOrder(Long id, String password, Admin admin);

    MessageResult completeOrder(Long id, String password, Admin admin);

    MessageResult confirmOrder(Long id, String password, Admin admin);

    MessageResult cancelOrder(Long id, String password, Admin admin);
}
