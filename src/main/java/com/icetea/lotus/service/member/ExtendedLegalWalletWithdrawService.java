package com.icetea.lotus.service.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.LegalWalletWithdrawScreen;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedLegalWalletWithdrawService {
    MessageResult page(PageModel pageModel, LegalWalletWithdrawScreen screen);

    MessageResult detail(Long id);

    MessageResult pass(Long id);

    MessageResult noPass(Long id);

    MessageResult remit(Long id, String paymentInstrument);
}
