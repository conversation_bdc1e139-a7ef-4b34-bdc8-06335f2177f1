package com.icetea.lotus.service.member;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.model.screen.MemberScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Date;

public interface ExtendedMemberService {
    MessageResult all();

    MessageResult detail(Long id);

    MessageResult delete(Long id);

    MessageResult update(Member member);

    MessageResult auditBusiness(Long id, CertifiedBusinessStatus status, String detail);

    MessageResult page(PageModel pageModel, MemberScreen screen);

    MessageResult getBusinessAuthApply(Long id, CertifiedBusinessStatus status);

    MessageResult outExcel(Date startTime, Date endTime, String account, HttpServletRequest request, HttpServletResponse response) throws Exception;

    MessageResult publishAdvertise(Long memberId, BooleanEnum status);

    MessageResult ban(CommonStatus status, Long memberId);

    MessageResult alterTransactionStatus(BooleanEnum status, Long memberId);

    MessageResult alterSuperPartner(String superPartner, Long memberId);

    MessageResult pageSuperPartner(PageModel pageModel, MemberScreen screen);

    MessageResult pageSuperMember(PageModel pageModel, MemberScreen screen, Long userId);

    MessageResult setInviter(Long id, Long inviterId) throws InterruptedException;
}
