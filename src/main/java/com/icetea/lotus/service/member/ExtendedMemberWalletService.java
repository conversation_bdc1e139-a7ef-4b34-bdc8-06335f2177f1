package com.icetea.lotus.service.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.model.screen.MemberWalletScreen;
import com.icetea.lotus.util.MessageResult;

import java.math.BigDecimal;

public interface ExtendedMemberWalletService {
    MessageResult getBalance(PageModel pageModel, MemberWalletScreen screen);

    MessageResult recharge(Admin admin, String unit, Long uid, BigDecimal amount);

    MessageResult resetAddress(String unit, long uid);

    MessageResult lockWallet(Long uid, String unit);

    MessageResult unlockWallet(Long uid, String unit);
}
