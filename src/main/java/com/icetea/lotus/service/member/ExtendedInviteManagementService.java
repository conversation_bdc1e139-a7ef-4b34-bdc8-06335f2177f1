package com.icetea.lotus.service.member;

import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.InviteManagementVO;
import com.icetea.lotus.vo.MemberInviteStasticVO;

import java.math.BigDecimal;

public interface ExtendedInviteManagementService {
    MessageResult lookAll(InviteManagementVO inviteManagementVO);

    MessageResult queryCondition(InviteManagementVO inviteManagementVO);

    MessageResult queryId(InviteManagementVO inviteManagementVO);

    MessageResult queryRankList(MemberInviteStasticVO memberInviteStasticVO);

    MessageResult updateRank(Long id, BigDecimal estimatedReward, BigDecimal extraReward, Integer levelOne, Integer levelTwo);

    MessageResult updateRank(Long id);
}
