package com.icetea.lotus.service.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.MemberApplicationScreen;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedMemberApplicationService {
    MessageResult all();

    MessageResult detail(Long id);

    MessageResult queryPage(PageModel pageModel, MemberApplicationScreen screen);

    MessageResult pass(Long id);

    MessageResult noPass(Long id, String rejectReason);
}
