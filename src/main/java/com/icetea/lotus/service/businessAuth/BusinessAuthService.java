package com.icetea.lotus.service.businessAuth;

import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.util.MessageResult;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttribute;

public interface BusinessAuthService {
    MessageResult getAll(PageModel pageModel, CommonStatus status);

    MessageResult create(Admin admin, Double amount, String coinUnit);

    MessageResult detail(Long id);

    MessageResult update(Long id, Double amount, CommonStatus status);

    MessageResult page(PageModel pageModel, CertifiedBusinessStatus status, String account);

    MessageResult getSearchStatus();
}
