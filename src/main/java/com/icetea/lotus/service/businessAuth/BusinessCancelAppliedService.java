package com.icetea.lotus.service.businessAuth;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.util.MessageResult;

import java.util.Date;

public interface BusinessCancelAppliedService {
    MessageResult pageQuery(PageModel pageModel, String account, CertifiedBusinessStatus status, Date startDate, Date endDate);

    MessageResult pass(Long id, BooleanEnum success, String reason);

    MessageResult detail(Long id);

    MessageResult getSearchStatus();
}
