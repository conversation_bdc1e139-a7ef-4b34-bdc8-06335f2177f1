/*
 * Copyright(C) 2025
 * AuthService.java, July 30,2025
 * thanhnv
 */
package com.icetea.lotus.service.auth;

import com.aliyuncs.ram.model.v20150501.ChangePasswordRequest;
import com.icetea.lotus.dto.RegisterRequest;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;

public interface AuthService {
    MessageResult login(LoginRequest loginRequest, HttpServletRequest request);

    MessageResult changePassword(ChangePasswordRequest changePasswordRequest, HttpServletRequest request);

    void blacklist(String token);

    boolean isBlacklisted(String token);

    String register(RegisterRequest registerRequest);
}
