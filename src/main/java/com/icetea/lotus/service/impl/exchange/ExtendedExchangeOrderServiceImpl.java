package com.icetea.lotus.service.impl.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.QExchangeOrder;
import com.icetea.lotus.es.ESUtils;
import com.icetea.lotus.model.screen.ExchangeOrderScreen;
import com.icetea.lotus.model.vo.ExchangeOrderOutVO;
import com.icetea.lotus.model.vo.ExchangeOrderVO;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.OrderDetailAggregationService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.exchange.ExtendedExchangeOrderService;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * The type Extended exchange order service.
 */
@Service
@Slf4j
public class ExtendedExchangeOrderServiceImpl extends BaseAdminController implements ExtendedExchangeOrderService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ExchangeOrderService exchangeOrderService;
    private final LocaleMessageSourceService messageSource;
    private final MemberService memberService;
    private final OrderDetailAggregationService orderDetailAggregationService;
    private final ESUtils esUtils;
    private final ExchangeCoinService coinService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public ExtendedExchangeOrderServiceImpl(BaseAdminService baseAdminService, KafkaTemplate<String, String> kafkaTemplate, ExchangeOrderService exchangeOrderService, LocaleMessageSourceService messageSource, MemberService memberService, OrderDetailAggregationService orderDetailAggregationService, ESUtils esUtils, ExchangeCoinService coinService) {
        super(baseAdminService);
        this.kafkaTemplate = kafkaTemplate;
        this.exchangeOrderService = exchangeOrderService;
        this.messageSource = messageSource;
        this.memberService = memberService;
        this.orderDetailAggregationService = orderDetailAggregationService;
        this.esUtils = esUtils;
        this.coinService = coinService;
    }


    @Override
    public MessageResult all() {
        List<ExchangeOrder> exchangeOrderList = exchangeOrderService.findAll();
        if (exchangeOrderList != null && !exchangeOrderList.isEmpty()) {
            return success(exchangeOrderList);
        }
        return error(messageSource.getMessage("NO_DATA"));
    }

    @Override
    public MessageResult detail(String id) {
        ExchangeOrder exchangeOrder = exchangeOrderService.findOne(id);
        Long memberId = exchangeOrder.getMemberId();
        Member member = memberService.findOne(memberId);
        ExchangeOrderVO exchangeOrderVO = new ExchangeOrderVO();
        BeanUtils.copyProperties(exchangeOrder, exchangeOrderVO);
        if (exchangeOrder.getTurnover() != null && exchangeOrder.getTurnover().compareTo(BigDecimal.ZERO) > 0 && exchangeOrder.getTradedAmount() != null && exchangeOrder.getTradedAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal price = exchangeOrder.getTurnover().divide(exchangeOrder.getTradedAmount(), 6, BigDecimal.ROUND_DOWN);
            exchangeOrderVO.setPrice(price);
        }
        exchangeOrderVO.setEmail(member.getEmail());
        exchangeOrderVO.setMobilePhone(member.getMobilePhone());
        exchangeOrderVO.setRealName(member.getRealName());
        exchangeOrderVO.setDetail(exchangeOrderService.getAggregation(id));

        return success(exchangeOrderVO);
    }

    @Override
    public MessageResult page(PageModel pageModel, ExchangeOrderScreen screen, HttpServletResponse response) throws IOException {
        if (pageModel.getDirection() == null && pageModel.getProperty() == null) {
            ArrayList<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setDirection(directions);
            List<String> property = new ArrayList<>();
            property.add("time");
            pageModel.setProperty(property);
        }
        // Get query conditions
        Predicate predicate = getPredicate(screen);
        if (screen.getIsOut() != null && screen.getIsOut() == 1) {
            Iterable<ExchangeOrder> allOut = exchangeOrderService.findAllOut(predicate);
            Set<Long> memberSet = new HashSet<>();
            allOut.forEach(v -> {
                memberSet.add(v.getMemberId());
            });
            Map<Long, Member> memberMap = memberService.mapByMemberIds(new ArrayList<>(memberSet));
            List<ExchangeOrderOutVO> voList = new ArrayList<>();
            allOut.forEach(v -> {
                ExchangeOrderOutVO vo = new ExchangeOrderOutVO();
                BeanUtils.copyProperties(v, vo);
                vo.setPrice(v.getType() == ExchangeOrderType.LIMIT_PRICE ? v.getPrice().toPlainString() : "MARKET_PRICE");
                if (v.getTurnover() != null && v.getTurnover().compareTo(BigDecimal.ZERO) > 0 && v.getTradedAmount() != null && v.getTradedAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal price = v.getTurnover().divide(v.getTradedAmount(), 6, BigDecimal.ROUND_DOWN);
                    vo.setPrice(price.toString());
                }
                vo.setAmount(v.getAmount().toPlainString());
                vo.setTradedAmount(v.getTradedAmount().toPlainString());
                vo.setType(v.getType() == ExchangeOrderType.MARKET_PRICE ? "MARKET_PRICE" : "LIMIT_PRICE");
                vo.setDirection(v.getDirection() == ExchangeOrderDirection.BUY ? "BUY" : "SELL");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                vo.setTime(sdf.format(new Date(v.getTime())));
                ExchangeOrderStatus status = v.getStatus();
                String strStatus = "--";
                if (status == ExchangeOrderStatus.TRADING) {
                    strStatus = "TRADING";
                } else if (status == ExchangeOrderStatus.COMPLETED) {
                    strStatus = "COMPLETED";
                } else if (status == ExchangeOrderStatus.CANCELED) {
                    strStatus = "CANCELED";
                }
                vo.setStatus(strStatus);
                Long memberId = vo.getMemberId();
                if (memberMap.containsKey(memberId)) {
                    Member member = memberMap.get(memberId);
                    vo.setEmail(member.getEmail());
                    vo.setMobilePhone(member.getMobilePhone());
                    vo.setRealName(member.getRealName());
                }
                voList.add(vo);
            });

            ExcelUtil.listToExcel(voList, ExchangeOrderOutVO.class.getDeclaredFields(), response.getOutputStream());
            return null;
        }
        Page<ExchangeOrder> all = exchangeOrderService.findAll(predicate, pageModel.getPageable());
        List<Long> memberIds = all.getContent().stream().distinct().map(ExchangeOrder::getMemberId).collect(Collectors.toList());
        Map<Long, Member> memberMap = memberService.mapByMemberIds(memberIds);
        Page<ExchangeOrderVO> page = all.map(v -> {
            ExchangeOrderVO exchangeOrderVO = new ExchangeOrderVO();
            BeanUtils.copyProperties(v, exchangeOrderVO);
            if (v.getTurnover() != null && v.getTurnover().compareTo(BigDecimal.ZERO) > 0 && v.getTradedAmount() != null && v.getTradedAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal price = v.getTurnover().divide(v.getTradedAmount(), 6, BigDecimal.ROUND_DOWN);
                exchangeOrderVO.setPrice(price);
            }
            Long memberId = exchangeOrderVO.getMemberId();
            if (memberMap.containsKey(memberId)) {
                Member member = memberMap.get(memberId);
                exchangeOrderVO.setEmail(member.getEmail());
                exchangeOrderVO.setMobilePhone(member.getMobilePhone());
                exchangeOrderVO.setRealName(member.getRealName());
            }
            return exchangeOrderVO;
        });
        return success(page);
    }

    private Predicate getPredicate(ExchangeOrderScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        QExchangeOrder qExchangeOrder = QExchangeOrder.exchangeOrder;
        if (screen.getOrderDirection() != null) {
            booleanExpressions.add(qExchangeOrder.direction.eq(screen.getOrderDirection()));
        }
        if (StringUtils.isNotEmpty(screen.getOrderId())) {
            booleanExpressions.add(qExchangeOrder.orderId.eq(screen.getOrderId()));
        }
        if (screen.getMemberId() != null) {
            booleanExpressions.add(qExchangeOrder.memberId.eq(screen.getMemberId()));
        }
        if (screen.getType() != null) {
            booleanExpressions.add(qExchangeOrder.type.eq(screen.getType()));
        }
        if (StringUtils.isNotBlank(screen.getCoinSymbol())) {
            booleanExpressions.add(qExchangeOrder.coinSymbol.equalsIgnoreCase(screen.getCoinSymbol()));
        }
        if (StringUtils.isNotBlank(screen.getBaseSymbol())) {
            booleanExpressions.add(qExchangeOrder.baseSymbol.equalsIgnoreCase(screen.getBaseSymbol()));
        }
        if (screen.getStatus() != null) {
            booleanExpressions.add(qExchangeOrder.status.eq(screen.getStatus()));
        }
        if (screen.getMinPrice() != null) {
            booleanExpressions.add(qExchangeOrder.price.goe(screen.getMinPrice()));
        }
        if (screen.getMaxPrice() != null) {
            booleanExpressions.add(qExchangeOrder.price.loe(screen.getMaxPrice()));
        }
        if (screen.getMinTradeAmount() != null) {
            booleanExpressions.add(qExchangeOrder.tradedAmount.goe(screen.getMinTradeAmount()));
        }
        if (screen.getMaxTradeAmount() != null) {
            booleanExpressions.add(qExchangeOrder.tradedAmount.loe(screen.getMaxTradeAmount()));
        }
        if (screen.getMinTurnOver() != null) {
            booleanExpressions.add(qExchangeOrder.turnover.goe(screen.getMinTurnOver()));
        }
        if (screen.getMaxTurnOver() != null) {
            booleanExpressions.add(qExchangeOrder.turnover.loe(screen.getMaxTurnOver()));
        }
        if (screen.getRobotOrder() != null && screen.getRobotOrder() == 1) {
            // Don't look at robots (not including robots)
            booleanExpressions.add(qExchangeOrder.memberId.notIn(1, 2, 10001));
// booleanExpressions.add(qExchangeOrder.memberId.notIn(69296 , 52350));
        }
        if (screen.getRobotOrder() != null && screen.getRobotOrder() == 0) {
            // View the robot
            booleanExpressions.add(qExchangeOrder.memberId.in(1, 2, 10001));
// booleanExpressions.add(qExchangeOrder.memberId.in(69296 , 52350));

        }
        if (screen.getCompleted() != null)
        /**
         * Entrusted Order
         */ {
            if (screen.getCompleted() == BooleanEnum.IS_FALSE) {
                booleanExpressions.add(qExchangeOrder.completedTime.isNull().and(qExchangeOrder.canceledTime.isNull())
                        .and(qExchangeOrder.status.eq(ExchangeOrderStatus.TRADING)));
            } else {
                /**
                 * Historical Orders
                 */
                booleanExpressions.add(qExchangeOrder.completedTime.isNotNull().or(qExchangeOrder.canceledTime.isNotNull())
                        .or(qExchangeOrder.status.ne(ExchangeOrderStatus.TRADING)));
            }
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }

    @Override
    public MessageResult outExcel(Long memberId, ExchangeOrderType type, String symbol, ExchangeOrderStatus status, ExchangeOrderDirection direction, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // Get query conditions
        List<Predicate> predicates = getPredicates(memberId, type, symbol, status, direction);
        List<ExchangeOrder> list = exchangeOrderService.queryWhereOrPage(predicates, null, null).getContent();
        return new FileUtil<ExchangeOrder>(messageSource).exportExcel(request, response, list, "order");
    }

    /**
     * Gets predicates.
     *
     * @param memberId  the member id
     * @param type      the type
     * @param symbol    the symbol
     * @param status    the status
     * @param direction the direction
     * @return the predicates
     */
// Get query conditions
    public List<Predicate> getPredicates(Long memberId, ExchangeOrderType type, String symbol, ExchangeOrderStatus status, ExchangeOrderDirection direction) {
        ArrayList<Predicate> predicates = new ArrayList<>();
        QExchangeOrder qExchangeOrder = QExchangeOrder.exchangeOrder;
        // predicates.add(qExchangeOrder.symbol.eq(QExchangeCoin.exchangeCoin.symbol));
        if (memberId != null) {
            predicates.add(qExchangeOrder.memberId.eq(memberId));
        }
        if (type != null) {
            predicates.add(qExchangeOrder.type.eq(type));
        }
        if (symbol != null) {
            predicates.add(qExchangeOrder.symbol.eq(symbol));
        }
        if (status != null) {
            predicates.add(qExchangeOrder.status.eq(status));
        }
        if (direction != null) {
            predicates.add(qExchangeOrder.direction.eq(direction));
        }
        return predicates;
    }

    @Override
    public MessageResult cancelOrder(String orderId) {
        ExchangeOrder order = exchangeOrderService.findOne(orderId);
        if (order.getStatus() != ExchangeOrderStatus.TRADING) {
            return MessageResult.error(500, "order not in trading");
        }
        // Send a message to the Exchange system
        try {
            kafkaTemplate.send("exchange-order-cancel", objectMapper.writeValueAsString(order));
            return MessageResult.success(messageSource.getMessage("SUCCESS"));
        } catch (JsonProcessingException e) {
            log.error("Error serializing order to JSON", e);
            return MessageResult.error("Error serializing order to JSON");
        }
    }

    @Override
    public MessageResult getOrderDetails(PageModel pageModel, Long memberId) {
        /*Criteria criteria = new Criteria();
        if(!StringUtils.isEmpty(memberId!=null){
            criteria.where("uidTo").is(message.getUidTo());
        }
        if(!StringUtils.isEmpty(message.getUidFrom())){
            criteria.where("uidFrom").is(message.getUidFrom());
        }*/
        // Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC,message.getSortFiled()));
        // Query query = new Query(criteria).with(sort);
        // EntityPage<ExchangeOrderDetailAggregation> result = exchangeOrderDetailAggregationService.findAllByPageNo(criteria,pageNo,pageSize);
        return success();
    }

    @Override
    public MessageResult findAllSymbol() {
        List<ExchangeCoin> coins = coinService.findAllEnabled();
        return success(coins);
    }

    @Override
    public MessageResult getExchangeOrderMineListByEs(Long memberId, String phone, String exchangeCoin, String startTime, String endTime, int page, int pageSize) {
        try {
            log.info(">>>>>>>>>>>>>Query Order List Start>>>>>>>>>>>>>");
            // Assembly query json
            StringBuffer query = new StringBuffer("{\"from\":" + (page - 1) * pageSize + ",\"query\":{\"bool\":{\"must\":[");
            if (memberId != null) {
                // Match user id
                query.append("{\"match\":{\"member_id\":\"" + memberId + "\"}}");
            }
            if (!org.springframework.util.StringUtils.isEmpty(phone)) {
                // Obtain user ID through mobile number
                Member member = memberService.findByPhone(phone);
                query.append("{\"match\":{\"member_id\":\"" + member.getId() + "\"}}");
            }
            if (!org.springframework.util.StringUtils.isEmpty(exchangeCoin)) {
                // Match trading pairs
                query.append("{\"match\":{\"symbol\":\"" + exchangeCoin + "\"}}");
            }
            if (!org.springframework.util.StringUtils.isEmpty(startTime) && !org.springframework.util.StringUtils.isEmpty(endTime)) {
                // Time range statement
                query.append("{\"range\":{\"transaction_time\":{\"gte\":\"" + startTime + "\",\"lte\":\"" + endTime + "\"}}}");
            }
            query.append("]}},\"size\":" + pageSize + ",\"sort\":[{\"transaction_time\":{\"order\":\"desc\"}}]}");

            try {
                ObjectNode objectNode = (ObjectNode) objectMapper.readTree(query.toString());
                return success(esUtils.query(objectNode));
            } catch (JsonProcessingException e) {
                log.error("Error parsing JSON query", e);
                return MessageResult.error("Error parsing JSON query");
            }

        } catch (Exception e) {
            log.info(">>>>>>>>>>>> Failed to query the order list >>>>>>>", e);
            return MessageResult.error("Failed to query the order list");
        }
    }
}
