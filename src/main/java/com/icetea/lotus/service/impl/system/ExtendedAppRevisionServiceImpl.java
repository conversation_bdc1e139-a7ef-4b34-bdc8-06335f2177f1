package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.AppRevision;
import com.icetea.lotus.model.create.AppRevisionCreate;
import com.icetea.lotus.model.update.AppRevisionUpdate;
import com.icetea.lotus.service.AppRevisionService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedAppRevisionService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * The type Extended app revision service.
 */
@Slf4j
@Service
public class ExtendedAppRevisionServiceImpl extends BaseAdminController implements ExtendedAppRevisionService {

    private final AppRevisionService service;

    public ExtendedAppRevisionServiceImpl(BaseAdminService baseAdminService, AppRevisionService service) {
        super(baseAdminService);
        this.service = service;
    }

    @Override
    public MessageResult create(AppRevisionCreate model) {
        service.save(model);
        return success();
    }

    @Override
    public MessageResult put(Long id, AppRevisionUpdate model) {
        AppRevision appRevision = service.findById(id);
        Assert.notNull(appRevision, "validate appRevision id!");
        service.update(model, appRevision);
        return success();
    }

    @Override
    public MessageResult get(Long id) {
        AppRevision appRevision = service.findById(id);
        Assert.notNull(appRevision, "validate appRevision id!");
        return success(appRevision);
    }

    @Override
    public MessageResult get(PageModel pageModel) {
        return success(service.findAll(null, pageModel));
    }
}
