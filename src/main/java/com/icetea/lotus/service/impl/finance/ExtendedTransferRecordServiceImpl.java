package com.icetea.lotus.service.impl.finance;

import com.icetea.lotus.client.WalletClient;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.entity.spot.QMember;
import com.icetea.lotus.entity.spot.QWalletTransRecord;
import com.icetea.lotus.service.finance.ExtendedTransferRecordService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.MemberTransactionVO;
import com.querydsl.core.types.Predicate;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;


@Service
@RequiredArgsConstructor
@Slf4j
public class ExtendedTransferRecordServiceImpl implements ExtendedTransferRecordService {
    private final WalletClient walletClient;

    @Override
    public MessageResult pageQuery(TransferRecordDTO screen) {
        try {
            return walletClient.getTransferHistory(screen);
        } catch (FeignException e) {
            log.error("Error fetching transfer history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch transfer history", e);
        }
    }
}
