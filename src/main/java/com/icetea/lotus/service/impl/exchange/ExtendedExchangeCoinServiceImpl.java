package com.icetea.lotus.service.impl.exchange;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.spot.QExchangeCoin;
import com.icetea.lotus.model.exchange.CustomRobotKline;
import com.icetea.lotus.model.exchange.ExchangeCoinRateRequest;
import com.icetea.lotus.model.exchange.RobotConfigRequest;
import com.icetea.lotus.model.exchange.RobotParams;
import com.icetea.lotus.model.screen.ExchangeCoinScreen;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.exchange.ExtendedExchangeCoinService;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.springframework.util.Assert.notNull;

/**
 * The type Extended exchange coin service.
 */
@Service
@Slf4j
public class ExtendedExchangeCoinServiceImpl extends BaseAdminController implements ExtendedExchangeCoinService {

    private final LocaleMessageSourceService messageSource;
    private final ExchangeCoinService exchangeCoinService;
    private final ExchangeOrderService exchangeOrderService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final RestTemplate restTemplate;
    private final CoinService coinService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${spark.system.md5.key}")
    private String md5Key;
    @Value("${cex-services.exchange:exchange}")
    private String exchangeServiceName;

    private static final String VALIDATE_SYMBOL_ERROR_MESSAGE = "validate symbol!";

    public ExtendedExchangeCoinServiceImpl(BaseAdminService baseAdminService, LocaleMessageSourceService messageSource,
                                           ExchangeCoinService exchangeCoinService, ExchangeOrderService exchangeOrderService,
                                           KafkaTemplate<String, String> kafkaTemplate, RestTemplate restTemplate, CoinService coinService) {
        super(baseAdminService);
        this.messageSource = messageSource;
        this.exchangeCoinService = exchangeCoinService;
        this.exchangeOrderService = exchangeOrderService;
        this.kafkaTemplate = kafkaTemplate;
        this.restTemplate = restTemplate;
        this.coinService = coinService;
    }

    /**
     * Add a new exchange trading pair if it does not already exist.
     *
     * @param exchangeCoin The exchange trading pair to be added.
     * @return MessageResult containing success or error information.
     */
    @Override
    public MessageResult exchangeCoinList(ExchangeCoin exchangeCoin) {
        log.info("Add exchange coin: {}", exchangeCoin.toString());

        ExchangeCoin findResult = exchangeCoinService.findBySymbol(exchangeCoin.getSymbol());
        if (findResult != null) {
            return error("[" + exchangeCoin.getSymbol() + "]The transaction pair already exists!");
        }
        Coin c1 = coinService.findByUnit(exchangeCoin.getBaseSymbol());
        if (c1 == null) {
            return error("[" + exchangeCoin.getBaseSymbol() + "] The settlement currency does not exist");
        }
        Coin c2 = coinService.findByUnit(exchangeCoin.getCoinSymbol());
        if (c2 == null) {
            return error("[" + exchangeCoin.getCoinSymbol() + "] The transaction currency does not exist");
        }
        exchangeCoin = exchangeCoinService.save(exchangeCoin);
        return success(exchangeCoin);
    }

    /**
     * Query paginated list of exchange coins with filtering and engine status.
     *
     * @param pageModel The pagination and sorting information.
     * @param screen    The filter criteria for the exchange coins.
     * @return MessageResult containing the paginated and enriched exchange coin list.
     */
    @Override
    public MessageResult exchangeCoinList(PageModel pageModel, ExchangeCoinScreen screen) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("sort");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.ASC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Predicate predicate = getPredicate(screen);
        Page<ExchangeCoin> all = exchangeCoinService.findAll(predicate, pageModel.getPageable());

        // Create HTTP headers to enforce JSON response
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        // Exchange Engine
        String exchangeUrl = "http://" + exchangeServiceName + "/exchange/monitor/engines"; //NOSONAR

        // Make API call with headers
        ResponseEntity<Map<String, Integer>> exchangeResult = restTemplate.exchange(
                exchangeUrl,
                HttpMethod.GET,
                requestEntity,
                new ParameterizedTypeReference<>() {
                }
        );
        Map<String, Integer> engineSymbols = exchangeResult.getBody();

        for (ExchangeCoin item : all.getContent()) {
            // 0: Not available
            item.setEngineStatus(Objects.requireNonNull(engineSymbols).getOrDefault(item.getSymbol(), 0)); // 1: Running 2: Pause
            item.setCurrentTime(Calendar.getInstance().getTimeInMillis());
        }

        // Market Engine
        String marketServiceName = "market";
        String marketUrl = "http://" + marketServiceName + "/market/engines";

        // Make API call with headers
        ResponseEntity<Map<String, Integer>> marketResult = restTemplate.exchange(
                marketUrl,
                HttpMethod.GET,
                requestEntity,
                new ParameterizedTypeReference<>() {
                }
        );
        Map<String, Integer> marketEngineSymbols = marketResult.getBody();

        for (ExchangeCoin item : all.getContent()) {
            // Market Engine
            item.setMarketEngineStatus(marketEngineSymbols.getOrDefault(item.getSymbol(), 0));

            // robot
            if (this.isRobotExists(item)) {
                item.setExEngineStatus(1);
            } else {
                item.setExEngineStatus(0);
            }
        }
        return success(all);
    }

    /**
     * Retrieve detailed information of a specific exchange coin by its symbol.
     *
     * @param symbol The symbol of the exchange coin to be retrieved.
     * @return MessageResult containing the exchange coin details if found.
     * @throws IllegalArgumentException if the exchange coin does not exist.
     */
    @Override
    public MessageResult detail(String symbol) {
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        return success(exchangeCoin);
    }

    @Override
    public MessageResult deletes(String[] ids) {
        // Check for unsold orders
        String coins = "";
        for (String id : ids) {
            ExchangeCoin temCoin = exchangeCoinService.findOne(id);
            notNull(temCoin, "ID=" + id + "The transaction pair does not exist");
            List<ExchangeOrder> orders = exchangeOrderService.findAllTradingOrderBySymbol(temCoin.getSymbol());
            if (!orders.isEmpty()) {
                return error(temCoin.getSymbol() + "There are still " + orders.size() + " commissions for transactions that have not been completed. Please cancel them before deleting them!");
            }
            if (temCoin.getEnable() == 1 || temCoin.getExchangeable() == 1) {
                return error("Please stop " + temCoin.getSymbol() + " trading engine first, and set the trading pair status to non-trading and removed status");
            }
            coins += temCoin.getSymbol() + ",";
        }
        log.info("Delete exchange coin: " + coins.substring(0, coins.length() - 1));
        exchangeCoinService.deletes(ids);
        return success();
    }

    @Override
    public MessageResult alterExchangeCoinRate(ExchangeCoinRateRequest exchangeCoinRateRequest, Admin admin) {
        // Mã hóa password
        String password = Encrypt.MD5(exchangeCoinRateRequest.getPassword() + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        // Tìm coin theo symbol
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(exchangeCoinRateRequest.getSymbol());
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        // Cập nhật các trường nếu có dữ liệu
        if (exchangeCoinRateRequest.getFee() != null) {
            exchangeCoin.setFee(exchangeCoinRateRequest.getFee()); // Modification fee
        }
        if (exchangeCoinRateRequest.getMinTurnover() != null) {
            exchangeCoin.setMinTurnover(exchangeCoinRateRequest.getMinTurnover());
        }
        if (exchangeCoinRateRequest.getMaxBuyPrice() != null) {
            exchangeCoin.setMaxBuyPrice(exchangeCoinRateRequest.getMaxBuyPrice());
        }
        if (exchangeCoinRateRequest.getSort() != null) {
            exchangeCoin.setSort(exchangeCoinRateRequest.getSort()); // Set the sort
        }
        if (exchangeCoinRateRequest.getEnable() != null && exchangeCoinRateRequest.getEnable() > 0 && exchangeCoinRateRequest.getEnable() < 3) {
            exchangeCoin.setEnable(exchangeCoinRateRequest.getEnable()); // Settings Enable Disable
        }
        if (exchangeCoinRateRequest.getVisible() != null && exchangeCoinRateRequest.getVisible() > 0 && exchangeCoinRateRequest.getVisible() < 3) {
            exchangeCoin.setVisible(exchangeCoinRateRequest.getVisible());
        }
        if (exchangeCoinRateRequest.getExchangeable() != null && exchangeCoinRateRequest.getExchangeable() > 0 && exchangeCoinRateRequest.getExchangeable() < 3) {
            exchangeCoin.setExchangeable(exchangeCoinRateRequest.getExchangeable());
        }
        if (exchangeCoinRateRequest.getEnableMarketBuy() != null && exchangeCoinRateRequest.getEnableMarketBuy() >= 0 && exchangeCoinRateRequest.getEnableMarketBuy() < 2) {
            exchangeCoin.setEnableMarketBuy(exchangeCoinRateRequest.getEnableMarketBuy() == 1 ? BooleanEnum.IS_TRUE : BooleanEnum.IS_FALSE);
        }
        if (exchangeCoinRateRequest.getEnableMarketSell() != null && exchangeCoinRateRequest.getEnableMarketSell() >= 0 && exchangeCoinRateRequest.getEnableMarketSell() < 2) {
            exchangeCoin.setEnableMarketSell(exchangeCoinRateRequest.getEnableMarketSell() == 1 ? BooleanEnum.IS_TRUE : BooleanEnum.IS_FALSE);
        }
        if (exchangeCoinRateRequest.getEnableBuy() != null && exchangeCoinRateRequest.getEnableBuy() >= 0 && exchangeCoinRateRequest.getEnableBuy() < 2) {
            exchangeCoin.setEnableBuy(exchangeCoinRateRequest.getEnableBuy() == 1 ? BooleanEnum.IS_TRUE : BooleanEnum.IS_FALSE);
        }
        if (exchangeCoinRateRequest.getEnableSell() != null && exchangeCoinRateRequest.getEnableSell() >= 0 && exchangeCoinRateRequest.getEnableSell() < 2) {
            exchangeCoin.setEnableSell(exchangeCoinRateRequest.getEnableSell() == 1 ? BooleanEnum.IS_TRUE : BooleanEnum.IS_FALSE);
        }
        if (exchangeCoinRateRequest.getFlag() != null && exchangeCoinRateRequest.getFlag() >= 0 && exchangeCoinRateRequest.getFlag() < 2) {
            exchangeCoin.setFlag(exchangeCoinRateRequest.getFlag());
        }
        if (exchangeCoinRateRequest.getFakeDataStatus() != null && exchangeCoinRateRequest.getFakeDataStatus() >= 0 && exchangeCoinRateRequest.getFakeDataStatus() < 2) {
            exchangeCoin.setFakeDataStatus(exchangeCoinRateRequest.getFakeDataStatus());
        }

        log.info("Modify exchange coin: " + exchangeCoinRateRequest.getSymbol());
        exchangeCoinService.save(exchangeCoin);

        return success();

    }

    @Override
    public MessageResult startExchangeCoinEngine(String symbol, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        if (exchangeCoin.getEnable() != 1) {
            return MessageResult.error(500, "Please set the transaction pair to enable (listed) status first");
        }
        
        String url = "http://" + exchangeServiceName + "/exchange/monitor/start-trader?symbol=" + symbol;
        ResponseEntity<MessageResult> resultStr = restTemplate.getForEntity(url, MessageResult.class);
        MessageResult result = resultStr.getBody();

        if (result.getCode() == 0) {
            log.info("Start exchange engine successful: " + symbol);
            return success();
        } else {
            log.info("Start exchange engine failed: " + symbol);
            return error(result.getMessage());
        }
    }

    @Override
    public MessageResult stopExchangeCoinEngine(String symbol, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        if (exchangeCoin.getExchangeable() != 2) {
            return MessageResult.error(500, "Please set the trading pair as non-trading first");
        }
        
        String url = "http://" + exchangeServiceName + "/exchange/monitor/stop-trader?symbol=" + symbol;
        ResponseEntity<MessageResult> resultStr = restTemplate.getForEntity(url, MessageResult.class);
        MessageResult result = resultStr.getBody();

        if (result.getCode() == 0) {
            log.info("Stop exchange engine successful: " + symbol);
            return success();
        } else {
            log.info("Stop exchange engine failed: " + symbol);
            return error(result.getMessage());
        }
    }

    @Override
    public MessageResult resetExchangeCoinEngine(String symbol, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        if (exchangeCoin.getExchangeable() != 1) {
            return MessageResult.error(500, "Please set the trading pair to be tradable status first");
        }
        
        String url = "http://" + exchangeServiceName + "/exchange/monitor/reset-trader?symbol=" + symbol;
        ResponseEntity<MessageResult> resultStr = restTemplate.getForEntity(url, MessageResult.class);
        MessageResult result = resultStr.getBody();

        if (result.getCode() == 0) {
            log.info("Reset exchange engine successful: {}", symbol);
            return success();
        } else {
            log.info("Reset exchange engine failed: {}", symbol);
            return error(result.getMessage());
        }
    }

    @Override
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<ExchangeCoin> all = exchangeCoinService.findAll();
        return new FileUtil<ExchangeCoin>(messageSource).exportExcel(request, response, all, "exchangeCoin");
    }

    @Override
    public MessageResult getAllBaseSymbolUnits() {
        List<String> list = exchangeCoinService.getBaseSymbol();
        return success(list);
    }

    @Override
    public MessageResult getAllCoinSymbolUnits(String baseSymbol) {
        List<String> list = exchangeCoinService.getCoinSymbol(baseSymbol);
        return success(list);
    }

    @Override
    public MessageResult cancelAllOrderBySymbol(String symbol, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        if (exchangeCoin.getExchangeable() != 2) {
            return MessageResult.error(500, "Please set the trading pair as non-trading first");
        }
        List<ExchangeOrder> orders = exchangeOrderService.findAllTradingOrderBySymbol(symbol);
        List<ExchangeOrder> cancelOrders = new ArrayList<ExchangeOrder>();
        for (ExchangeOrder order : orders) {
            if (order.getStatus() != ExchangeOrderStatus.TRADING) {
                continue;
            }
            if (isExchangeOrderExist(order)) {
                try {
                    log.info("Cancel exchange order: ({}) {}", symbol, objectMapper.writeValueAsString(orders));
                    kafkaTemplate.send("exchange-order-cancel", objectMapper.writeValueAsString(order));
                } catch (JsonProcessingException e) {
                    log.error("Error serializing order to JSON", e);
                }
                cancelOrders.add(order);
            } else {
                // Forced cancellation
                //exchangeOrderService.forceCancelOrder(order); //NOSONAR
            }
        }

        return success("No transaction number of commissions:" + orders.size() + ", Successfully canceled:" + cancelOrders.size(), cancelOrders);
    }

    @Override
    public MessageResult overviewExchangeCoin(String symbol) {
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        
        String url = "http://" + exchangeServiceName + "/exchange/monitor/overview?symbol=" + symbol;

        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                url,
                HttpMethod.GET,
                requestEntity,
                new ParameterizedTypeReference<Map<String, Object>>() {}
        );
        Map<String, Object> result = resultStr.getBody();

        log.info("Overview exchange coin: " + symbol);
        return success(result);
    }

    @Override
    public MessageResult getRobotConfig(String symbol) {
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        if (exchangeCoin.getRobotType() == 0) {
            String serviceName = "ROBOT-TRADE-NORMAL";
            String contextPath = "/ernormal";
            String url = "http://" + serviceName + "/ernormal/getRobotParams?coinName=" + symbol;
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Get robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    return success(data);
                } else {
                    return error("Failed to obtain the robot parameters (the transaction was stopped unexpectedly without the robot or the robot)!");
                }
            } catch (Exception e) {
                e.printStackTrace();
                return error("Failed to obtain the robot parameters (the transaction was stopped unexpectedly without the robot or the robot)!");
            }
        } else if (exchangeCoin.getRobotType() == 1) { // It is independent, and it is easier to modify the disk control robot later. In fact, the code is the same now.
            String serviceName = "ROBOT-TRADE-NORMAL";
            String contextPath = "/ernormal";
            String url = "http://" + serviceName + "/ernormal/getRobotParams?coinName=" + symbol;
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Get robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    return success(data);
                } else {
                    return error("Failed to get the robot parameters (the transaction was stopped unexpectedly for no robot or robot)!");
                }
            } catch (Exception e) {
                e.printStackTrace();
                return error("Failed to get the robot parameters (the transaction was stopped unexpectedly for no robot or robot)!");
            }
        } else if (exchangeCoin.getRobotType() == 2) {
            // Disk control robot
            return null;
        } else {
            return null;
        }
    }

    /**
     * Detect whether there is a trading robot
     *
     * @param coin
     * @return
     */
    private boolean isRobotExists(ExchangeCoin coin) {
        if (coin.getRobotType() == 0) {
            String serviceName = "ROBOT-TRADE-NORMAL";
            String url = "http://" + serviceName + "/ernormal/getRobotParams?coinName=" + coin.getSymbol();
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Get robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                return result != null && Integer.valueOf(0).equals(result.get("code"));
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        } else if (coin.getRobotType() == 1) { // It is independent, and it is easier to modify the disk control robot later. In fact, the code is the same now.
            String serviceName = "ROBOT-TRADE-NORMAL"; // Disk control robot也通过此处进行控制
            String url = "http://" + serviceName + "/ernormal/getRobotParams?coinName=" + coin.getSymbol();
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Get robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                return result != null && Integer.valueOf(0).equals(result.get("code"));
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        } else if (coin.getRobotType() == 2) {
            return false;
        } else {
            return false;
        }
    }

    @Override
    public MessageResult alterRobotConfig(RobotConfigRequest robotConfigRequest, Admin admin) {
        // Lấy thông tin coin từ symbol
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(robotConfigRequest.getSymbol());
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        // Kiểm tra loại robot
        if (exchangeCoin.getRobotType() == 0 || exchangeCoin.getRobotType() == 1) {
            RobotParams params = new RobotParams();
            params.setCoinName(robotConfigRequest.getSymbol());
            params.setHalt(robotConfigRequest.getIsHalt() != 0); // 0 -> false, non-zero -> true
            params.setStartAmount(robotConfigRequest.getStartAmount());
            params.setRandRange0(robotConfigRequest.getRandRange0());
            params.setRandRange1(robotConfigRequest.getRandRange1());
            params.setRandRange2(robotConfigRequest.getRandRange2());
            params.setRandRange3(robotConfigRequest.getRandRange3());
            params.setRandRange4(robotConfigRequest.getRandRange4());
            params.setRandRange5(robotConfigRequest.getRandRange5());
            params.setRandRange6(robotConfigRequest.getRandRange6());
            params.setScale(robotConfigRequest.getScale());
            params.setAmountScale(robotConfigRequest.getAmountScale());
            params.setMaxSubPrice(robotConfigRequest.getMaxSubPrice());
            params.setInitOrderCount(robotConfigRequest.getInitOrderCount());
            params.setPriceStepRate(robotConfigRequest.getPriceStepRate());
            params.setRunTime(robotConfigRequest.getRunTime());
            params.setRobotType(exchangeCoin.getRobotType());

            // Lấy chiến lược robot từ dịch vụ
            String serviceName = "ROBOT-TRADE-NORMAL";
            String contextPath = "/ernormal";
            String url = "http://" + serviceName + "/ernormal/getRobotParams?coinName=" + robotConfigRequest.getSymbol();
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.GET,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Get robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    params.setStrategyType(((Number) data.get("strategyType")).intValue());
                    params.setFlowPair((String) data.get("flowPair"));
                    params.setFlowPercent(new BigDecimal(data.get("flowPercent").toString()));
                } else {
                    return error("Failed to get the robot parameters (the transaction was stopped unexpectedly for no robot or robot)!");
                }
            } catch (Exception e) {
                log.error("Error getting robot parameters: ", e);
                return error("Failed to get the robot parameters (the transaction was stopped unexpectedly for no robot or robot)!");
            }

            // Cập nhật robot configuration
            url = "http://" + serviceName + "/ernormal/setRobotParams";
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<RobotParams> requestEntity = new HttpEntity<>(params, headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("Set robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();
                if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                    return success(result);
                } else {
                    return error("Modifying the robot parameters failed (the transaction was stopped unexpectedly for no robot or robot)!");
                }
            } catch (Exception e) {
                log.error("Error setting robot parameters: ", e);
                return error("Modifying the robot parameters failed (the transaction was stopped unexpectedly for no robot or robot)!");
            }
        } else {
            return error("Failed to modify the robot parameters: This trading pair is not an ordinary robot!");
        }
    }

    @Override
    public MessageResult createRobotConfig(RobotConfigRequest robotConfigRequest, Admin admin) {
        // Retrieve exchange coin based on the symbol
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(robotConfigRequest.getSymbol());
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);

        // Validate if the robot type is either general or disk control robots
        if (isValidRobotType(exchangeCoin)) {
            RobotParams params = buildRobotParams(robotConfigRequest, exchangeCoin);

            // Define the service URL based on robot type
            String url = buildUrl(exchangeCoin.getRobotType());

            // Create the robot using the REST service
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<RobotParams> requestEntity = new HttpEntity<>(params, headers);

                ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                );
                log.info("create robot config: " + resultStr);
                Map<String, Object> result = resultStr.getBody();

                if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                    return success(result);
                } else {
                    return error("Creation failed: " + (result != null ? result.get("message") : "Unknown error"));
                }
            } catch (Exception e) {
                return error("Create the robot failed (the transaction was stopped unexpectedly without the robot or the robot was stopped)!");
            }
        } else {
            return error("Failed to create a robot: This trading pair is not an ordinary robot!");
        }
    }

    @Override
    public MessageResult createRobotKlineData(String symbol, String kdate, String kline, Integer pricePencent) {
        // Check currency type
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        if (exchangeCoin.getRobotType() != 1) {
            return error("This trading pair is not a disk control robot");
        }
        if (kdate.equals("") || kdate.length() < 10) {
            return error("Date incoming error!");
        }
        kdate = kdate.substring(0, 10); // The form of the front desk transmission is: 2020-12-01T16:00:00.000Z

        // keep
        String serviceName = "ROBOT-TRADE-NORMAL";
        String url = "http://" + serviceName + "/ernormal/setRobotStrategy?coinName=" + symbol + "&strategy=2&flowPair=" + "BTC/USDT" + "&flowPercent=1";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            Map<String, Object> result = resultStr.getBody();
            if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                // do nothing
            } else {
                return error("Please create a robot first");
            }
        } catch (Exception e) {
            return error("Saving failed");
        }

        // Construct parameters
        CustomRobotKline params = new CustomRobotKline();
        params.setCoinName(symbol);
        params.setKdate(kdate);
        params.setKline(kline);
        params.setPricePencent(pricePencent);

        serviceName = "ROBOT-TRADE-NORMAL";
        url = "http://" + serviceName + "/ernormal/saveKline";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<CustomRobotKline> requestEntity = new HttpEntity<>(params, headers);

            ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            log.info("save robot kline: " + resultStr);
            Map<String, Object> result = resultStr.getBody();
            if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                return success(result);
            } else {
                return error("Creation failed:" + (result != null ? result.get("message") : "Unknown error"));
            }
        } catch (Exception e) {
            return error("Create the robot failed (the transaction was stopped unexpectedly without the robot or the robot was stopped)!");
        }
    }

    @Override
    public MessageResult createRobotFlow(String symbol, String pair, BigDecimal flowPercent) {
        // Check currency type
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        if (exchangeCoin.getRobotType() != 1) {
            return error("This trading pair is not a disk control robot");
        }
        if (StringUtils.isEmpty(pair)) {
            return error("Please select the following trading pair");
        }

        // keepString coinName, Integer strategy, String flowPair, BigDecimal flowPercent
        String serviceName = "ROBOT-TRADE-NORMAL";
        String url = "http://" + serviceName + "/ernormal/setRobotStrategy?coinName=" + symbol + "&strategy=1&flowPair=" + pair + "&flowPercent=" + flowPercent;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            Map<String, Object> result = resultStr.getBody();
            if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                @SuppressWarnings("unchecked")
                List<Object> data = (List<Object>) result.get("data");
                return success(data);
            } else {
                log.info("Failed to obtain the robot K-line parameters");
                return error("Failed to obtain the robot K-line parameters (the transaction was stopped unexpectedly without the robot or the robot)!");
            }
        } catch (Exception e) {
            return error("Saving failed");
        }
    }

    @Override
    public MessageResult customRobotCoinList() {
        List<ExchangeCoin> coinList = exchangeCoinService.findAllByRobotType(1);
        return success(coinList);
    }

    @Override
    public MessageResult robotKlineDataList(String symbol, String kdate) {
        ExchangeCoin exchangeCoin = exchangeCoinService.findOne(symbol);
        notNull(exchangeCoin, VALIDATE_SYMBOL_ERROR_MESSAGE);
        if (exchangeCoin.getRobotType() != 1) {
            return error("This trading pair is not a disk control robot");
        }

        kdate = kdate.substring(0, 10); // The form of the front desk transmission is: 2020-12-01T16:00:00.000Z

        String currentDate = kdate;
        // Get the current date and the next date by default
        if (currentDate.equals("") || currentDate == null) {
            Date date = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            currentDate = sf.format(date);
        }

        String serviceName = "ROBOT-TRADE-NORMAL";
        String contextPath = "/ernormal";
        String url = "http://" + serviceName + "/ernormal/getRobotKline?coinName=" + symbol + "&kdate=" + currentDate;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> resultStr = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            log.info("Get robot kline data: " + resultStr);
            Map<String, Object> result = resultStr.getBody();
            if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                @SuppressWarnings("unchecked")
                List<Object> data = (List<Object>) result.get("data");
                return success(data);
            } else {
                log.info("Failed to obtain the robot K-line parameters");
                return error("Failed to obtain the robot K-line parameters (the transaction was stopped unexpectedly without the robot or the robot)!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("Failed to obtain the robot K-line parameters (the transaction was stopped unexpectedly without the robot or the robot)!");
        }
    }

    // Helper method to check if the robot type is valid
    private boolean isValidRobotType(ExchangeCoin exchangeCoin) {
        return exchangeCoin.getRobotType() == 0 || exchangeCoin.getRobotType() == 1;
    }

    // Helper method to build RobotParams object from the request
    private RobotParams buildRobotParams(RobotConfigRequest robotConfigRequest, ExchangeCoin exchangeCoin) {
        RobotParams params = new RobotParams();
        params.setCoinName(robotConfigRequest.getSymbol());
        params.setHalt(robotConfigRequest.getIsHalt().intValue() != 0); // Set halt based on request value
        params.setStartAmount(robotConfigRequest.getStartAmount());
        params.setRandRange0(robotConfigRequest.getRandRange0());
        params.setRandRange1(robotConfigRequest.getRandRange1());
        params.setRandRange2(robotConfigRequest.getRandRange2());
        params.setRandRange3(robotConfigRequest.getRandRange3());
        params.setRandRange4(robotConfigRequest.getRandRange4());
        params.setRandRange5(robotConfigRequest.getRandRange5());
        params.setRandRange6(robotConfigRequest.getRandRange6());
        params.setScale(robotConfigRequest.getScale());
        params.setAmountScale(robotConfigRequest.getAmountScale());
        params.setMaxSubPrice(robotConfigRequest.getMaxSubPrice());
        params.setInitOrderCount(robotConfigRequest.getInitOrderCount());
        params.setPriceStepRate(robotConfigRequest.getPriceStepRate());
        params.setRunTime(robotConfigRequest.getRunTime());
        params.setRobotType(exchangeCoin.getRobotType());
        params.setStrategyType(2); // Default customization
        params.setFlowPair("BTC/USDT"); // Default BTC/USDT
        params.setFlowPercent(BigDecimal.valueOf(1));
        return params;
    }

    // Helper method to build the URL based on robot type
    private String buildUrl(int robotType) {
        String serviceName = "ROBOT-TRADE-NORMAL";
        if (robotType == 1) {
            return "http://" + serviceName + "/ernormal/createCustomRobot";
        }
        return "http://" + serviceName + "/ernormal/createRobot";
    }

    /**
     * Is exchange order exist boolean.
     *
     * @param order the order
     * @return the boolean
     */
    public boolean isExchangeOrderExist(ExchangeOrder order) {
        try {
            String url = "http://" + exchangeServiceName + "/exchange/monitor/order?symbol=" + order.getSymbol() + "&orderId=" + order.getOrderId() + "&direction=" + order.getDirection() + "&type=" + order.getType();
            ResponseEntity<ExchangeOrder> result = restTemplate.getForEntity(url, ExchangeOrder.class);
            return result != null;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private Predicate getPredicate(ExchangeCoinScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        QExchangeCoin qExchangeCoin = QExchangeCoin.exchangeCoin;
        if (StringUtils.isNotBlank(screen.getCoinSymbol())) {
            booleanExpressions.add(qExchangeCoin.coinSymbol.equalsIgnoreCase(screen.getCoinSymbol()));
        }
        if (StringUtils.isNotBlank(screen.getBaseSymbol())) {
            booleanExpressions.add(qExchangeCoin.baseSymbol.equalsIgnoreCase(screen.getBaseSymbol()));
        }
        if (booleanExpressions.isEmpty()) {
            booleanExpressions.add(qExchangeCoin.symbol.isNotNull());
        }

        return PredicateUtils.getPredicate(booleanExpressions);
    }
}
