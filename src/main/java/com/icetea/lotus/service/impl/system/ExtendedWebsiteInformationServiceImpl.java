package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.WebsiteInformation;
import com.icetea.lotus.service.WebsiteInformationService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedWebsiteInformationService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * The type Extended website information service.
 */
@Service
public class ExtendedWebsiteInformationServiceImpl extends BaseAdminController implements ExtendedWebsiteInformationService {

    private final WebsiteInformationService websiteInformationService;

    public ExtendedWebsiteInformationServiceImpl(BaseAdminService baseAdminService, WebsiteInformationService websiteInformationService) {
        super(baseAdminService);
        this.websiteInformationService = websiteInformationService;
    }

    @Override
    public MessageResult get() {
        WebsiteInformation one = websiteInformationService.fetchOne();
        if (one == null) {
            return error("Please add settings!(admin/websiteInformation/modify)");
        }
        return success("get success", one);
    }

    @Override
    public MessageResult modify(WebsiteInformation websiteInformation) {
        WebsiteInformation one = websiteInformationService.fetchOne();
        if (one == null) {
            websiteInformation.setId(1L);
        } else {
            websiteInformation.setId(one.getId());
        }
        WebsiteInformation save = websiteInformationService.save(websiteInformation);
        return success(save);
    }
}
