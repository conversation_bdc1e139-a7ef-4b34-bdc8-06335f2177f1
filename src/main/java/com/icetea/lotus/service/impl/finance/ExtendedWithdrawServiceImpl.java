package com.icetea.lotus.service.impl.finance;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.constant.WithdrawStatus;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinprotocolDTO;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QMember;
import com.icetea.lotus.entity.spot.QWithdraw;
import com.icetea.lotus.entity.spot.QWithdrawRecord;
import com.icetea.lotus.entity.spot.Withdraw;
import com.icetea.lotus.entity.spot.WithdrawRecord;
import com.icetea.lotus.es.ESUtils;
import com.icetea.lotus.model.screen.WithdrawRecordScreen;
import com.icetea.lotus.model.screen.WithdrawScreen;
import com.icetea.lotus.model.vo.WithdrawExcelVO;
import com.icetea.lotus.model.vo.WithdrawVO;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.WithdrawRecordService;
import com.icetea.lotus.service.WithdrawService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedWithdrawService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.icetea.lotus.vendor.provider.SMSProvider;
import com.icetea.lotus.vo.WithdrawRecordVO;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.icetea.lotus.constant.BooleanEnum.IS_FALSE;
import static com.icetea.lotus.constant.WithdrawStatus.FAIL;
import static com.icetea.lotus.constant.WithdrawStatus.COMPLETED;
import static com.icetea.lotus.constant.WithdrawStatus.WAITING;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

/**
 * The type Extended withdraw service.
 */
@Service
public class ExtendedWithdrawServiceImpl extends BaseAdminController implements ExtendedWithdrawService {

    private final CoinService coinService;

    private final CoinprotocolService coinprotocolService;

    private final MemberService memberService;

    private final WithdrawService withdrawService;

    private final WithdrawRecordService withdrawRecordService;

    private final MemberWalletService memberWalletService;

    private final MemberTransactionService memberTransactionService;

    private final LocaleMessageSourceService messageSource;

    private final ESUtils esUtils;

    private final SMSProvider smsProvider;

    @Value("${spark.system.md5.key}")
    private String md5Key;

    public ExtendedWithdrawServiceImpl(BaseAdminService baseAdminService, CoinService coinService, CoinprotocolService coinprotocolService, MemberService memberService, WithdrawService withdrawService, WithdrawRecordService withdrawRecordService, MemberWalletService memberWalletService, MemberTransactionService memberTransactionService, LocaleMessageSourceService messageSource, ESUtils esUtils, SMSProvider smsProvider) {
        super(baseAdminService);
        this.coinService = coinService;
        this.coinprotocolService = coinprotocolService;
        this.memberService = memberService;
        this.withdrawService = withdrawService;
        this.withdrawRecordService = withdrawRecordService;
        this.memberWalletService = memberWalletService;
        this.memberTransactionService = memberTransactionService;
        this.messageSource = messageSource;
        this.esUtils = esUtils;
        this.smsProvider = smsProvider;
    }

    @Override
    public MessageResult coinList() {
        List<CoinDTO> list = coinService.list();

        return success(list);
    }

    @Override
    public MessageResult protocolList() {
        List<CoinprotocolDTO> list = coinprotocolService.list();

        return success(list);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, WithdrawScreen withdrawScreen, HttpServletResponse response) throws IOException {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        String email = withdrawScreen.getEmail();
        if (!StringUtils.isBlank(email)) {
            Member byEmail = memberService.findByEmail(email);
            if (byEmail != null) {
                booleanExpressions.add(QWithdraw.withdraw.memberid.eq(byEmail.getId()));
            }
        }
        String tel = withdrawScreen.getTel();
        if (!StringUtils.isBlank(tel)) {
            Member byPhone = memberService.findByPhone(tel);
            if (byPhone != null) {
                booleanExpressions.add(QWithdraw.withdraw.memberid.eq(byPhone.getId()));
            }
        }


        String address = withdrawScreen.getAddress();
        if (!StringUtils.isBlank(address)) {
            booleanExpressions.add(QWithdraw.withdraw.address.eq(address));
        }

        Integer protocol = withdrawScreen.getProtocol();
        if (protocol != null && protocol > 0) {
            booleanExpressions.add(QWithdraw.withdraw.protocol.eq(protocol));
        }

        String coinname = withdrawScreen.getCoinname();
        if (!StringUtils.isBlank(coinname)) {
            booleanExpressions.add(QWithdraw.withdraw.coinname.eq(coinname));
        }

        Integer status = withdrawScreen.getStatus();
        if (status != null && status > -2) {
            booleanExpressions.add(QWithdraw.withdraw.status.eq(status));
        }

        String hash = withdrawScreen.getHash();
        if (!StringUtils.isBlank(hash)) {
            booleanExpressions.add(QWithdraw.withdraw.hash.eq(hash));
        }

        String startAddTime = withdrawScreen.getStartAddTime();
        String endAddTime = withdrawScreen.getEndAddTime();
        if (!StringUtils.isBlank(startAddTime) && !StringUtils.isBlank(endAddTime)) {
            Date startAddTimeDate = null;
            Date endAddTimeDate = null;
            try {
                startAddTimeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startAddTime);
                endAddTimeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endAddTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (startAddTimeDate != null && endAddTimeDate != null) {
                booleanExpressions.add(QWithdraw.withdraw.addtime.between(startAddTimeDate.getTime(),endAddTimeDate.getTime()));
            }
        }

        String startProcessTime = withdrawScreen.getStartProcessTime();
        String endProcessTime = withdrawScreen.getEndProcessTime();
        if (!StringUtils.isBlank(startProcessTime) && !StringUtils.isBlank(endProcessTime)) {
            Date startProcessTimeDate = null;
            Date endProcessTimeDate = null;
            try {
                startProcessTimeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startProcessTime);
                endProcessTimeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endProcessTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (startProcessTimeDate != null && endProcessTimeDate != null) {
                booleanExpressions.add(QWithdraw.withdraw.processtime.between(startProcessTimeDate.getTime(),endProcessTimeDate.getTime()));
            }
        }

        // log.info("condition: {}", booleanExpressions);

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);


        // Export
        if (withdrawScreen.getIsOut() == 1) {

            Iterable<Withdraw> allOut = withdrawService.findAllOut(predicate);
            Set<Long> memberSet = new HashSet<>();
            allOut.forEach(v -> {
                memberSet.add(Long.valueOf(v.getMemberid()));
            });
            Map<Long, Member> memberMap = memberService.mapByMemberIds(new ArrayList<>(memberSet));

            List<WithdrawExcelVO> voList = new ArrayList<>();


            allOut.forEach(v -> {
                WithdrawExcelVO vo = new WithdrawExcelVO();
                BeanUtils.copyProperties(v, vo);

                vo.setMemberId(Long.valueOf(v.getMemberid()));

                vo.setMoney(String.valueOf(v.getMoney()));

                vo.setFee(String.valueOf(v.getFee()));

                vo.setReal_money(String.valueOf(v.getReal_money()));

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                vo.setAddtime(sdf.format(new Date(v.getAddtime())));
                if (v.getProcesstime() != null && v.getProcesstime() > 0) {
                    vo.setProcesstime(sdf.format(new Date(v.getProcesstime())));
                } else {
                    vo.setProcesstime("--");
                }

                Integer statusD = v.getStatus();
                String statusStr = "";
                if (statusD == -1) {
                    statusStr = "Turn down";
                } else if (statusD == 0) {
                    statusStr = "Pending";
                } else if (statusD == 1) {
                    statusStr = "Processing";
                } else if (statusD == 2) {
                    statusStr = "Processed";
                } else {
                    statusStr = "Fail";
                }
                vo.setStatus(statusStr);

                Long memberId = vo.getMemberId();
                if (memberMap.containsKey(memberId)) {
                    Member member = memberMap.get(memberId);
                    vo.setEmail(member.getEmail());
                    vo.setMobilePhone(member.getMobilePhone());
                }
                voList.add(vo);

            });

            ExcelUtil.listToExcel(voList, WithdrawExcelVO.class.getDeclaredFields(), response.getOutputStream());

            return null;

        }


        Page<Withdraw> all = withdrawService.findAll(predicate, pageModel.getPageable());

        List<Long> memberIds = all.getContent().stream().map(v -> (long) v.getMemberid()).collect(Collectors.toList());
        Map<Long, Member> memberMap = memberService.mapByMemberIds(memberIds);

        Page<WithdrawVO> page = all.map(v -> {
            WithdrawVO withdrawVO = new WithdrawVO();
            BeanUtils.copyProperties(v, withdrawVO);
            Long memberid = (long) withdrawVO.getMemberid();
            if (memberMap.containsKey(memberid)) {
                withdrawVO.setUsername(memberMap.get(memberid).getUsername());
                withdrawVO.setEmail(memberMap.get(memberid).getEmail());
            }
            return withdrawVO;
        });

        return success(page);
    }

    @Override
    public MessageResult merge(Withdraw withdraw, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        if (withdraw.getId() == null || withdraw.getId() <= 0) {
            result = error("Please select the audit record");
            return result;
        }

        Withdraw one = withdrawService.findOne(withdraw.getId());
        if (one == null) {
            result = error("Please select the audit record");
            return result;
        }

        Withdraw update = new Withdraw();
        update.setId(withdraw.getId());
        update.setStatus(withdraw.getStatus());
        if (withdraw.getStatus() == -1) {
            update.setWithdrawinfo(withdraw.getWithdrawinfo());
        }

        if (StringUtils.isBlank(withdraw.getWithdrawinfo())) {
            update.setWithdrawinfo("");
        }
        update.setProcesstime(new Date().getTime());
        update.setCoinname(one.getCoinname());
        update.setMemberid(one.getMemberid());
        update.setMoney(one.getMoney());
        withdrawService.save(update);

        result = success("Operation is successful");
        result.setData(withdraw);
        return result;
    }

    @Override
    public MessageResult all() {
        List<WithdrawRecord> withdrawRecordList = withdrawRecordService.findAll();
        if (withdrawRecordList == null || withdrawRecordList.size() < 1) {
            return error(messageSource.getMessage("NO_DATA"));
        }
        return success(withdrawRecordList);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, WithdrawRecordScreen screen) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(QMember.member.id));

        if (screen.getMemberId() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(screen.getMemberId()));
        }

        if ( !org.springframework.util.StringUtils.isEmpty(screen.getMobilePhone())){
            Member member = memberService.findByPhone(screen.getMobilePhone());
            predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(member.getId()));
        }

        if ( !org.springframework.util.StringUtils.isEmpty(screen.getOrderSn())){
            predicates.add(QWithdrawRecord.withdrawRecord.transactionNumber.eq(screen.getOrderSn()));
        }

        if (screen.getStatus() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.status.eq(screen.getStatus()));
        }

        if (screen.getIsAuto() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.isAuto.eq(screen.getIsAuto()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getAddress())) {
            predicates.add(QWithdrawRecord.withdrawRecord.address.eq(screen.getAddress()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getUnit())) {
            predicates.add(QWithdrawRecord.withdrawRecord.coin.unit.equalsIgnoreCase(screen.getUnit()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }

        Page<WithdrawRecordVO> pageListMapResult = withdrawRecordService.joinFind(predicates, pageModel);
        return success(pageListMapResult);
    }

    @Override
    public MessageResult detail(Long id) {
        WithdrawRecord withdrawRecord = withdrawRecordService.findOne(id);
        notNull(withdrawRecord, messageSource.getMessage("NO_DATA"));
        return success(withdrawRecord);
    }

    @Override
    public MessageResult auditPass(Long[] ids) {
        withdrawRecordService.audit(ids, WAITING);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = df.format(new Date());
        for(Long id : ids) {
            WithdrawRecord record = withdrawRecordService.findOne(id);
            Member member = memberService.findOne(record.getMemberId());
            if(member != null) {
                try {
                    smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, congratulations on" + date + "extract" + record.getCoin().getUnit() + "Successful, number of" + record.getArrivedAmount() + ".");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return success(messageSource.getMessage("PASS_THE_AUDIT"));
    }

    @Override
    public MessageResult auditNoPass(Long[] ids) {
        withdrawRecordService.audit(ids, FAIL);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = df.format(new Date());
        for(Long id : ids) {
            WithdrawRecord record = withdrawRecordService.findOne(id);
            Member member = memberService.findOne(record.getMemberId());
            if(member != null) {
                try {
                    smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, I am very sorry, you" + date + "extract" + record.getCoin().getUnit() + "Failed, the number is" + record.getArrivedAmount() + ", please contact customer service for processing.");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return success(messageSource.getMessage("AUDIT_DOES_NOT_PASS"));
    }

    @Override
    public MessageResult addNumber(Long id, String transactionNumber) {
        WithdrawRecord record = withdrawRecordService.findOne(id);
        Assert.notNull(record, "The record does not exist");
        Assert.isTrue(record.getIsAuto() == BooleanEnum.IS_FALSE, "This cash withdrawal order is automatically reviewed");
        record.setTransactionNumber(transactionNumber);
        record.setStatus(COMPLETED);
        MemberWallet memberWallet = memberWalletService.findByCoinAndMemberId(record.getCoin(), record.getMemberId());
        Assert.notNull(memberWallet, "member id " + record.getMemberId() + "The wallet is null");
        memberWallet.setFrozenBalance(memberWallet.getFrozenBalance().subtract(record.getTotalAmount()));
        memberWalletService.save(memberWallet);
        record = withdrawRecordService.save(record);

        MemberTransaction memberTransaction = new MemberTransaction();
        memberTransaction.setMemberId(record.getMemberId());
        memberTransaction.setAddress(record.getAddress());
        memberTransaction.setAmount(record.getTotalAmount());
        memberTransaction.setSymbol(record.getCoin().getUnit());
        memberTransaction.setCreateTime(record.getCreateTime());
        memberTransaction.setType(TransactionType.WITHDRAW);
        memberTransaction.setFee(record.getFee());
        memberTransaction.setRealFee(record.getFee()+"");
        memberTransaction.setDiscountFee("0");
        memberTransaction= memberTransactionService.save(memberTransaction);

        return MessageResult.success(messageSource.getMessage("SUCCESS"), record);
    }

    @Override
    public MessageResult remittance(Admin admin, Long[] ids, String transactionNumber, String password) {
        Assert.notNull(admin, messageSource.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));
        password = Encrypt.MD5(password + md5Key);
        if (!password.equals(admin.getPassword())) {
            return error(messageSource.getMessage("WRONG_PASSWORD"));
        }
        WithdrawRecord withdrawRecord;
        for (Long id : ids) {
            withdrawRecord = withdrawRecordService.findOne(id);
            notNull(withdrawRecord, "id:" + id + messageSource.getMessage("NO_DATA"));
            isTrue(withdrawRecord.getStatus() == WAITING, "The withdrawal status is not waiting for coins to be released, and the money cannot be transferred!");
            isTrue(withdrawRecord.getIsAuto() == IS_FALSE, "Not manual review and withdrawal!");
            // Mark withdrawal completed
            withdrawRecord.setStatus(COMPLETED);
            // Transaction code
            withdrawRecord.setTransactionNumber(transactionNumber);
            MemberWallet memberWallet = memberWalletService.findByCoinAndMemberId(withdrawRecord.getCoin(), withdrawRecord.getMemberId());
            Assert.notNull(memberWallet, "member id" + withdrawRecord.getMemberId() + "The wallet is null");
            memberWallet.setFrozenBalance(memberWallet.getFrozenBalance().subtract(withdrawRecord.getTotalAmount()));
            memberWalletService.save(memberWallet);
            withdrawRecordService.save(withdrawRecord);

            MemberTransaction memberTransaction = new MemberTransaction();
            memberTransaction.setMemberId(withdrawRecord.getMemberId());
            memberTransaction.setAddress(withdrawRecord.getAddress());
            memberTransaction.setAmount(withdrawRecord.getTotalAmount());
            memberTransaction.setSymbol(withdrawRecord.getCoin().getUnit());
            memberTransaction.setCreateTime(withdrawRecord.getCreateTime());
            memberTransaction.setType(TransactionType.WITHDRAW);
            memberTransaction.setFee(withdrawRecord.getFee());
            memberTransaction.setRealFee(withdrawRecord.getFee()+"");
            memberTransaction.setDiscountFee("0");
            memberTransaction= memberTransactionService.save(memberTransaction);

        }
        return success();
    }

    @Override
    public MessageResult pageQuerySuper(PageModel pageModel, WithdrawRecordScreen screen, Long memberId) {
        Member checkMember = memberService.findOne(memberId);
        if(!checkMember.getSuperPartner().equals("1")) {
            return error("You are not an agent!");
        }

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(QMember.member.id));
        predicates.add(QMember.member.inviterId.eq(checkMember.getId()));

        if (screen.getMemberId() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(screen.getMemberId()));
        }

        if ( !org.springframework.util.StringUtils.isEmpty(screen.getMobilePhone())){
            Member member = memberService.findByPhone(screen.getMobilePhone());
            predicates.add(QWithdrawRecord.withdrawRecord.memberId.eq(member.getId()));
        }

        if ( !org.springframework.util.StringUtils.isEmpty(screen.getOrderSn())){
            predicates.add(QWithdrawRecord.withdrawRecord.transactionNumber.eq(screen.getOrderSn()));
        }

        if (screen.getStatus() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.status.eq(screen.getStatus()));
        }

        if (screen.getIsAuto() != null) {
            predicates.add(QWithdrawRecord.withdrawRecord.isAuto.eq(screen.getIsAuto()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getAddress())) {
            predicates.add(QWithdrawRecord.withdrawRecord.address.eq(screen.getAddress()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getUnit())) {
            predicates.add(QWithdrawRecord.withdrawRecord.coin.unit.equalsIgnoreCase(screen.getUnit()));
        }

        if (!org.springframework.util.StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }

        Page<WithdrawRecordVO> pageListMapResult = withdrawRecordService.joinFind(predicates, pageModel);
        return success(pageListMapResult);
    }
}
