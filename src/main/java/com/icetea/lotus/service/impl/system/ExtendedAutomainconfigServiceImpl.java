package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinprotocolDTO;
import com.icetea.lotus.entity.spot.AutomainSetPassword;
import com.icetea.lotus.entity.spot.Automainconfig;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.entity.spot.MessageEncrypt;
import com.icetea.lotus.service.AutomainconfigService;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedAutomainconfigService;
import com.icetea.lotus.util.AESUtil;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.util.List;

/**
 * The type Extended automainconfig service.
 */
@Slf4j
@Service
public class ExtendedAutomainconfigServiceImpl extends BaseAdminController implements ExtendedAutomainconfigService {

    private final CoinService coinService;
    private final CoinprotocolService coinprotocolService;
    private final AutomainconfigService automainconfigService;
    private final RedisTemplate<String, String> redisTemplate;
    private final RestTemplate restTemplate;

    public ExtendedAutomainconfigServiceImpl(BaseAdminService baseAdminService, CoinService coinService, CoinprotocolService coinprotocolService, AutomainconfigService automainconfigService, RedisTemplate<String, String> redisTemplate, RestTemplate restTemplate) {
        super(baseAdminService);
        this.coinService = coinService;
        this.coinprotocolService = coinprotocolService;
        this.automainconfigService = automainconfigService;
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
    }

    @Override
    public MessageResult coinList() {
        List<CoinDTO> list = coinService.list();
        return success(list);
    }

    @Override
    public MessageResult protocolList() {

        List<CoinprotocolDTO> list = coinprotocolService.list();
        return success(list);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {

        BooleanExpression predicate = null;
        Page<Automainconfig> all = automainconfigService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult merge(Automainconfig automainconfig) {
        MessageResult result;
        // Check if the query exists
        Automainconfig one = automainconfigService.findOne(automainconfig.getCoinname(), automainconfig.getProtocol());
        if (automainconfig.getId() != null) {
            if (one != null && !one.getId().equals(automainconfig.getId())) {
                result = error("The currency of the current protocol already exists");
                return result;
            }
        } else if (one != null) {
            result = error("The currency of the current protocol already exists");
            return result;
        }

        // Delete the redis cache
        redisTemplate.delete("automainconfig");

        automainconfig = automainconfigService.save(automainconfig);
        result = success("Operation is successful");
        result.setData(automainconfig);
        return result;
    }

    @Override
    public MessageResult collectCoin(Automainconfig automainconfig) {
        Coinprotocol protocol = coinprotocolService.findByProtocol(automainconfig.getProtocol());
        // Remote RPC service URL, suffix is currency unit
        String serviceName = "SERVICE-RPC-" + protocol.getSymbol();
        String url = "http://" + serviceName + "/rpc/transferAll?address={1}&coinName={2}&password={3}";
        ResponseEntity<MessageResult> response = restTemplate.getForEntity(url, MessageResult.class, automainconfig.getAddress(), automainconfig.getCoinname(), automainconfig.getPassword().trim());
        log.info("remote call:service={},response={}", serviceName, response);
        if (response.getStatusCode().value() == 200) {
            if (response.getBody().getCode() == 0) {
                return success("Operation is successful");
            } else {
                return error(response.getBody().getMessage());
            }
        } else {
            return error("Operation failed");
        }
    }

    @Override
    public MessageResult setPassword(AutomainSetPassword automainconfig) {
        if (StringUtils.isEmpty(automainconfig.getPassword())) {
            return error("Password cannot be empty");
        }
        Coinprotocol protocol = coinprotocolService.findByProtocol(automainconfig.getProtocol());
        // Remote RPC service URL, suffix is currency unit
        String serviceName = "SERVICE-RPC-" + protocol.getSymbol();
        String url = "http://" + serviceName + "/rpc/setPassword?password={1}";
        ResponseEntity<MessageResult> response = restTemplate.getForEntity(url, MessageResult.class, automainconfig.getPassword().trim());
        log.info("remote call:service={},response={}", serviceName, response);
        if (response.getStatusCode().value() == 200) {
            if (response.getBody().getCode() == 0) {
                return success("Operation is successful");
            } else {
                return error(response.getBody().getMessage());
            }
        } else {
            return error("Operation failed");
        }
    }

    @Override
    public MessageResult updateContract(AutomainSetPassword automainconfig) {
        if (StringUtils.isEmpty(automainconfig.getPassword())) {
            return error("Password cannot be empty");
        }
        Coinprotocol protocol = coinprotocolService.findByProtocol(automainconfig.getProtocol());
        // Remote RPC service URL, suffix is currency unit
        String serviceName = "SERVICE-RPC-" + protocol.getSymbol();
        String url = "http://" + serviceName + "/rpc/updateContract?password={1}";
        ResponseEntity<MessageResult> response = restTemplate.getForEntity(url, MessageResult.class,automainconfig.getPassword().trim());
        log.info("remote call:service={},response={}", serviceName, response);
        if (response.getStatusCode().value() == 200) {
            if (response.getBody().getCode() == 0) {
                return success("Operation is successful");
            } else {
                return error(response.getBody().getMessage());
            }
        } else {
            return error("Operation failed");
        }
    }

    @Override
    public MessageResult encrypt(MessageEncrypt messageEncrypt) throws Exception {
        if (StringUtils.isEmpty(messageEncrypt.getPassword())) {
            return error("Password cannot be empty");
        }
        String encrypt = AESUtil.encrypt(messageEncrypt.getMessage(), messageEncrypt.getPassword().trim());
        return success("Operation is successful", encrypt);
    }
}
