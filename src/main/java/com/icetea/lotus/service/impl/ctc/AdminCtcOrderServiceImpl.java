package com.icetea.lotus.service.impl.ctc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.CtcAcceptor;
import com.icetea.lotus.entity.spot.CtcOrder;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.service.*;
import com.icetea.lotus.service.ctc.AdminCtcOrderService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Admin ctc order service.
 */
@Service
@RequiredArgsConstructor
public class AdminCtcOrderServiceImpl extends BaseController implements AdminCtcOrderService {
    private final CtcOrderService ctcOrderService;

    private final MemberWalletService memberWalletService;

    private final MemberTransactionService memberTransactionService;

    private final LocaleMessageSourceService messageSource;

    private final CtcAcceptorService acceptorService;

    @Value("${spark.system.md5.key}")
    private String md5Key;

    private final SMSProvider smsProvider;

    private final MemberService memberService;

    @Override
    public MessageResult orderList(PageModel pageModel) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<CtcOrder> all = ctcOrderService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult orderDetail(Long id) {
        if(id == null || id == 0) {
            return error("The order does not exist");
        }
        CtcOrder order = ctcOrderService.findOne(id);
        if(order == null) {
            return error("The order does not exist");
        }
        return success(order);
    }

    @Override
    public MessageResult payOrder(Long id, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        CtcOrder order = ctcOrderService.findOne(id);
        notNull(order, "validate order.id!");
        if(order.getStatus() != 1) {
            return error("Unable to mark payment for status orders outside the accepted status");
        }
        if(order.getDirection() != 1) {
            return error("This order was bought by the user and cannot be marked for payment");
        }
        order.setStatus(2);
        order.setPayTime(DateUtil.getCurrentDate());
        ctcOrderService.save(order);
        return success();
    }

    @Override
    public MessageResult completeOrder(Long id, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        CtcOrder order = ctcOrderService.findOne(id);
        notNull(order, "validate order.id!");

        if(order.getStatus() != 2) {
            return error("Please confirm that the order has been paid!");
        }
        List<CtcAcceptor> acceptors = acceptorService.findByMember(order.getAcceptor());// findOne(order.getAcceptor().getId());
        if(acceptors.size() != 1) {
            return error("Acceptor mapping relationship error!");
        }
        CtcAcceptor acceptor = acceptors.get(0);
        // Buy scenario => User wallet balance increases
        if(order.getDirection() == 0) {
            MemberWallet mw = memberWalletService.findByCoinUnitAndMemberId(order.getUnit(), order.getMember().getId());
            if(mw == null) {
                return error("User wallet does not exist");
            }
            memberWalletService.increaseBalance(mw.getId(), order.getAmount());

            MemberTransaction memberTransaction = new MemberTransaction();
            memberTransaction.setFee(BigDecimal.ZERO);
            memberTransaction.setAmount(order.getAmount());
            memberTransaction.setMemberId(mw.getMemberId());
            memberTransaction.setSymbol(order.getUnit());
            memberTransaction.setType(TransactionType.CTC_BUY);
            memberTransaction.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction.setRealFee("0");
            memberTransaction.setDiscountFee("0");
            memberTransaction= memberTransactionService.save(memberTransaction);

            // Acceptor Account Change (USDT, CNY)
            acceptor.setUsdtOut(acceptor.getUsdtOut().add(order.getAmount())); // Sold USDT increases
            acceptor.setCnyIn(acceptor.getCnyIn().add(order.getMoney())); // RMB income increased
            acceptorService.saveAndFlush(acceptor);

            Member member = memberService.findOne(order.getMember().getId());
            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, your order number is"
                        + order.getOrderSn()
                        + ", the unit price of the purchase is" + order.getPrice() + "CNY"
                        + ", the quantity is"+ order.getAmount() + "USDT"
                        + ", the total price is" + order.getMoney()
                        + "The order has been confirmed by the acceptance provider and the coins are released. Please check it in time.");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // Selling scenario =>Deduction of frozen assets by user wallet
        if(order.getDirection() == 1) {
            MemberWallet mw = memberWalletService.findByCoinUnitAndMemberId(order.getUnit(), order.getMember().getId());
            if(mw == null) {
                return error("User wallet does not exist");
            }
            if(mw.getFrozenBalance().compareTo(order.getAmount()) < 0) {
                return error("User freeze balance insufficient");
            }
            memberWalletService.decreaseFrozen(mw.getId(), order.getAmount());


            MemberTransaction memberTransaction = new MemberTransaction();
            memberTransaction.setFee(BigDecimal.ZERO);
            memberTransaction.setAmount(order.getAmount().negate());
            memberTransaction.setMemberId(mw.getMemberId());
            memberTransaction.setSymbol(order.getUnit());
            memberTransaction.setType(TransactionType.CTC_SELL);
            memberTransaction.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction.setRealFee("0");
            memberTransaction.setDiscountFee("0");
            memberTransaction= memberTransactionService.save(memberTransaction);

            acceptor.setUsdtIn(acceptor.getUsdtIn().add(order.getAmount())); // Accounting USDT increased
            acceptor.setCnyOut(acceptor.getCnyOut().add(order.getMoney())); // RMB payments increase
            acceptorService.saveAndFlush(acceptor);

            Member member = memberService.findOne(order.getMember().getId());
            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, your order number is"
                        + order.getOrderSn()
                        + ", the unit price of the seller is" + order.getPrice() + "CNY"
                        + ", the quantity is"+ order.getAmount() + "USDT"
                        + ", the total price is" + order.getMoney()
                        + "The order has been confirmed and paid by the acceptance provider. Please check it in time.");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        order.setStatus(3);
        order.setCompleteTime(DateUtil.getCurrentDate());
        ctcOrderService.save(order);
        return success();
    }

    @Override
    public MessageResult confirmOrder(Long id, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        CtcOrder order = ctcOrderService.findOne(id);
        notNull(order, "validate order.id!");
        if(order.getStatus() != 0) {
            return error("Unable to accept orders outside the state of unsuccessful orders");
        }
        order.setStatus(1);
        order.setConfirmTime(DateUtil.getCurrentDate());
        ctcOrderService.save(order);

        Member member = memberService.findOne(order.getMember().getId());
        try {
            smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, your order number is"
                    + order.getOrderSn()
                    + ", unit price is" + order.getPrice() + "CNY"
                    + ", the quantity is"+ order.getAmount() + "USDT"
                    + ", the total price is" + order.getMoney()
                    + "The order has been accepted by the acceptance provider, please wait patiently for the other party to pay.");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return success();
    }

    @Override
    public MessageResult cancelOrder(Long id, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        CtcOrder order = ctcOrderService.findOne(id);
        notNull(order, "validate order.id!");

        if(order.getStatus() == 3 || order.getStatus() == 4) {
            return error("Order status is completed or cancelled");
        }

        if(order.getDirection() == 1) {
            // Sell ​​orders, need to thaw assets
            MemberWallet memberWallet = memberWalletService.findByCoinUnitAndMemberId(order.getUnit(), order.getMember().getId());
            if(memberWallet.getFrozenBalance().compareTo(order.getAmount()) >= 0) {
                memberWalletService.thawBalance(memberWallet, order.getAmount());
            }else {
                return error("User balance is not enough to thaw");
            }
        }
        order.setStatus(4);
        order.setCancelReason("Administrator forced cancel");
        order.setCancelTime(DateUtil.getCurrentDate());
        ctcOrderService.save(order);

        Member member = memberService.findOne(order.getMember().getId());
        try {
            smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, your order number is"
                    + order.getOrderSn()
                    + "The order has been forced to be cancelled by the platform.");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return success("Forced cancellation successfully");
    }
}
