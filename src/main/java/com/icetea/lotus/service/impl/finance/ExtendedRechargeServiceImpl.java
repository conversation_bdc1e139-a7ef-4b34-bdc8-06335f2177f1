package com.icetea.lotus.service.impl.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.WithdrawStatus;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinprotocolDTO;
import com.icetea.lotus.entity.spot.*;
import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.model.screen.RechargeScreen;
import com.icetea.lotus.dto.response.DepositRecordResponse;
import com.icetea.lotus.model.vo.RechargeExcelVO;
import com.icetea.lotus.model.vo.RechargeVO;
import com.icetea.lotus.service.*;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedRechargeService;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * The type Extended recharge service.
 */
@Service
@Slf4j
public class ExtendedRechargeServiceImpl extends BaseAdminController implements ExtendedRechargeService {
    private final CoinService coinService;

    private final CoinprotocolService coinprotocolService;

    private final RechargeService rechargeService;

    private final MemberService memberService;

    private final DepositRecordService depositRecordService;

    public ExtendedRechargeServiceImpl(BaseAdminService baseAdminService, CoinService coinService, CoinprotocolService coinprotocolService,
                                       RechargeService rechargeService, MemberService memberService,
                                       DepositRecordService depositRecordService) {
        super(baseAdminService);
        this.coinService = coinService;
        this.coinprotocolService = coinprotocolService;
        this.rechargeService = rechargeService;
        this.memberService = memberService;
        this.depositRecordService = depositRecordService;
    }


    @Override
    public MessageResult coinList() {
        List<CoinDTO> list = coinService.list();

        return success(list);
    }

    @Override
    public MessageResult protocolList() {
        List<CoinprotocolDTO> list = coinprotocolService.list();

        return success(list);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, RechargeScreen rechargeScreen, HttpServletResponse response) throws IOException {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        String address = rechargeScreen.getAddress();
        if (!StringUtils.isBlank(address)) {
            booleanExpressions.add(QRecharge.recharge.address.eq(address));
        }

        Integer protocol = rechargeScreen.getProtocol();
        if (protocol != null && protocol > 0) {
            booleanExpressions.add(QRecharge.recharge.protocol.eq(protocol));
        }

        String coinName = rechargeScreen.getCoinname();
        if (!StringUtils.isBlank(coinName)) {
            booleanExpressions.add(QRecharge.recharge.coinname.eq(coinName));
        }

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);

        // Export
        if (rechargeScreen.getIsOut() == 1) {
            Iterable<Recharge> allOut = rechargeService.findAllOut(predicate);
            Set<Long> memberSet = new HashSet<>();
            allOut.forEach(v -> memberSet.add(Long.valueOf(v.getMemberid())));
            Map<Long, Member> memberMap = memberService.mapByMemberIds(new ArrayList<>(memberSet));

            List<RechargeExcelVO> voList = new ArrayList<>();

            allOut.forEach(v -> {
                RechargeExcelVO vo = new RechargeExcelVO();
                BeanUtils.copyProperties(v, vo);

                vo.setMemberId(Long.valueOf(v.getMemberid()));

                vo.setMoney(String.valueOf(v.getMoney()));

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (v.getAddtime() != null && v.getAddtime() > 0) {
                    vo.setAddtime(sdf.format(new Date(v.getAddtime())));
                } else {
                    vo.setAddtime("--");
                }

                Integer status = v.getStatus();
                String statusStr = "";
                if (status == 0) {
                    statusStr = "Not received";
                } else if (status == 1) {
                    statusStr = "Accounted";
                } else {
                    statusStr = "Fail";
                }
                vo.setStatus(statusStr);

                vo.setConfirms(v.getConfirms() + "/" + v.getNconfirms());

                Long memberId = vo.getMemberId();
                if (memberMap.containsKey(memberId)) {
                    Member member = memberMap.get(memberId);
                    vo.setEmail(member.getEmail());
                    vo.setMobilePhone(member.getMobilePhone());
                }
                voList.add(vo);

            });

            ExcelUtil.listToExcel(voList, RechargeExcelVO.class.getDeclaredFields(), response.getOutputStream());

            return null;
        }


        Page<Recharge> all = rechargeService.findAll(predicate, pageModel.getPageable());

        List<Long> memberIds = all.getContent().stream().map(v -> (long) v.getMemberid()).toList();
        Map<Long, Member> memberMap = memberService.mapByMemberIds(memberIds);

        Page<RechargeVO> page = all.map(v -> {
            RechargeVO rechargeVO = new RechargeVO();
            BeanUtils.copyProperties(v, rechargeVO);
            Long memberId = (long) rechargeVO.getMemberid();
            if (memberMap.containsKey(memberId)) {
                rechargeVO.setUsername(memberMap.get(memberId).getUsername());
            }
            return rechargeVO;
        });


        return success(page);
    }


    /**
    * PageQuery is the method used for checking & building criteria filter and handling to get deposit records with pagination
    * @param pageModel pagination request
    * @param depositScreenRequest filter request
    * @param response HttpServletResponse
    * @return a message contains a list of deposit records with pagination
    */
    @Override
    public MessageResult pageQuery(PageModel pageModel, DepositScreenRequest depositScreenRequest, HttpServletResponse response) throws IOException {
        log.info("Check deposit screen {}", depositScreenRequest);
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        String userId = depositScreenRequest.getUserid();
        try {
            if (!StringUtils.isBlank(userId)) {
                Long.parseLong(userId);
                booleanExpressions.add(QDepositRecord.depositRecord.member.id.stringValue().containsIgnoreCase(userId));
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("User Id must be valid number");
        }

        String username = depositScreenRequest.getUsername();
        if (!StringUtils.isBlank(username)) {
            booleanExpressions.add(QDepositRecord.depositRecord.member.username.containsIgnoreCase(username));
        }

        String email = depositScreenRequest.getEmail();
        if(!StringUtils.isBlank(email)) {
            booleanExpressions.add(QDepositRecord.depositRecord.member.email.containsIgnoreCase(email));
        }

        try{
            Instant startDate = (depositScreenRequest.getFromDate() != null) ? Instant.parse(depositScreenRequest.getFromDate()) : null;
            if (startDate != null) {
                booleanExpressions.add(QDepositRecord.depositRecord.createdAt.goe(startDate));
            }

            Instant endDate = (depositScreenRequest.getToDate() != null) ? Instant.parse(depositScreenRequest.getToDate()) : null;
            if (endDate != null) {
                booleanExpressions.add(QDepositRecord.depositRecord.createdAt.loe(endDate));
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Please use ISO-8601 format like yyyy-MM-ddTHH:mm:ssZ");
        }

        Integer status = depositScreenRequest.getStatus();
        if (status != null) {
            WithdrawStatus statusEnum = WithdrawStatus.values() [status];
            booleanExpressions.add(QDepositRecord.depositRecord.status.eq(statusEnum));
        }

        String coinName = depositScreenRequest.getCoinName();
        if (!StringUtils.isBlank(coinName)) {
            coinName = coinName.toUpperCase();
            booleanExpressions.add(QDepositRecord.depositRecord.coin.name.eq(coinName));
        }

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);

        // Handle export excel file
//            if (depositScreen.getIsOut() == 1) {
//                Iterable<DepositRecord> allOut = depositRecordService.findAll(predicate);
//                Set<Long> memberSet = new HashSet<>();
//                allOut.forEach(v -> {
//                    memberSet.add(Long.valueOf(String.valueOf(v.getMember())));
//                });
//                Map<Long, Member> memberMap = memberService.mapByMemberIds(new ArrayList<>(memberSet));
//
//                List<RechargeExcelVO> voList = new ArrayList<>();
//
//                allOut.forEach(v -> {
//                    RechargeExcelVO vo = new RechargeExcelVO();
//                    BeanUtils.copyProperties(v, vo);
//
//                    vo.setMemberId(Long.valueOf(v.getMember()));
//
//                    vo.setMoney(String.valueOf(v.getMoney()));
//
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    if (v.getAddtime() != null && v.getAddtime() > 0) {
//                        vo.setAddtime(sdf.format(new Date(v.getAddtime())));
//                    } else {
//                        vo.setAddtime("--");
//                    }
//
//                    Integer status = v.getStatus();
//                    String statusStr = "";
//                    if (status == 0) {
//                        statusStr = "Not received";
//                    } else if (status == 1) {
//                        statusStr = "Accounted";
//                    } else {
//                        statusStr = "Fail";
//                    }
//                    vo.setStatus(statusStr);
//
//                    vo.setConfirms(v.getConfirms() + "/" + v.getNconfirms());
//
//                    Long memberId = vo.getMemberId();
//                    if (memberMap.containsKey(memberId)) {
//                        Member member = memberMap.get(memberId);
//                        vo.setEmail(member.getEmail());
//                        vo.setMobilePhone(member.getMobilePhone());
//                    }
//                    voList.add(vo);
//
//                });
//
//                ExcelUtil.listToExcel(voList, RechargeExcelVO.class.getDeclaredFields(), response.getOutputStream());
//
//                return null;
//            }

        Page<DepositRecord> all = depositRecordService.list(predicate, pageModel);

        List<Long> memberIds = all.getContent().stream().map(v -> v.getMember().getId()).toList();
        Map<Long, Member> memberMap = memberService.mapByMemberIds(memberIds);

        Page<DepositRecordResponse> page = all.map(v -> {
            DepositRecordResponse depositRecordResponse = new DepositRecordResponse();
            BeanUtils.copyProperties(v, depositRecordResponse);

            if(v.getProtocol() != null) {
                Coinprotocol coinprotocol = coinprotocolService.findByProtocol(v.getProtocol());
                if(coinprotocol != null) {
                    depositRecordResponse.setNetwork(coinprotocol.getProtocolname());
                }
            }

            if(v.getCoin() != null) {
                depositRecordResponse.setCoinName(v.getCoin().getName());
                depositRecordResponse.setCoinUnit(v.getCoin().getUnit());
            }

            if(v.getMember() != null) {
                depositRecordResponse.setMemberId(v.getMember().getId());
                Long memberId = Long.valueOf(String.valueOf(depositRecordResponse.getMemberId()));
                if (memberMap.containsKey(memberId)) {
                    depositRecordResponse.setUsername(memberMap.get(memberId).getUsername());
                    depositRecordResponse.setEmail(memberMap.get(memberId).getEmail());
                }
            }
            return depositRecordResponse;
        });
        return success(page);
    }
}
