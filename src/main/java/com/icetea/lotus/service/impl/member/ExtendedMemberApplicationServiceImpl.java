package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberApplication;
import com.icetea.lotus.model.screen.MemberApplicationScreen;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberApplicationService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedMemberApplicationService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.icetea.lotus.vendor.provider.SMSProvider;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static com.icetea.lotus.entity.spot.QMemberApplication.memberApplication;
import static org.springframework.util.Assert.notNull;

/**
 * The type Extended member application service.
 */
@Service
public class ExtendedMemberApplicationServiceImpl extends BaseAdminController implements ExtendedMemberApplicationService {

    private final SMSProvider smsProvider;
    private final MemberApplicationService memberApplicationService;
    private final LocaleMessageSourceService messageSource;

    public ExtendedMemberApplicationServiceImpl(BaseAdminService baseAdminService, SMSProvider smsProvider, MemberApplicationService memberApplicationService, LocaleMessageSourceService messageSource) {
        super(baseAdminService);
        this.smsProvider = smsProvider;
        this.memberApplicationService = memberApplicationService;
        this.messageSource = messageSource;
    }

    @Override
    public MessageResult all() {
        List<MemberApplication> all = memberApplicationService.findAll();
        if (all != null && !all.isEmpty()) {
            return success(all);
        }
        return error(messageSource.getMessage("NO_DATA"));
    }

    @Override
    public MessageResult detail(Long id) {
        MemberApplication memberApplication = memberApplicationService.findOne(id);
        notNull(memberApplication, "validate id!");
        return success(memberApplication);
    }

    @Override
    public MessageResult queryPage(PageModel pageModel, MemberApplicationScreen screen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (screen.getAuditStatus() != null) {
            booleanExpressions.add(memberApplication.auditStatus.eq(screen.getAuditStatus()));
        }
        if (StringUtils.hasLength(screen.getAccount())) {
            booleanExpressions.add(memberApplication.member.username.like("%" + screen.getAccount() + "%")
                    // .or(memberApplication.member.mobilePhone.like(screen.getAccount() + "%"))
                    // .or(memberApplication.member.email.like(screen.getAccount() + "%"))
                    .or(memberApplication.member.realName.like("%" + screen.getAccount() + "%")));
        }
        if (StringUtils.hasLength(screen.getCardNo())) {
            booleanExpressions.add(memberApplication.member.idNumber.like("%" + screen.getCardNo() + "%"));
        }
        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<MemberApplication> all = memberApplicationService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult pass(Long id) {
        // check
        MemberApplication application = memberApplicationService.findOne(id);
        notNull(application, "validate id!");
        // business
        memberApplicationService.auditPass(application);
        // Send notification
        try {
            smsProvider.sendCustomMessage(application.getMember().getMobilePhone(), "Congratulations!The real-name authentication application you submitted has passed the review!");
        } catch (Exception e) {
            return error(e.getMessage());
        }
        // return
        return success();
    }

    @Override
    public MessageResult noPass(Long id, String rejectReason) {
        // check
        MemberApplication application = memberApplicationService.findOne(id);
        notNull(application, "validate id!");
        // business
        application.setRejectReason(rejectReason);// Reason for rejection
        memberApplicationService.auditNotPass(application);

        try {
            smsProvider.sendCustomMessage(application.getMember().getMobilePhone(), "The real-name authentication application you submitted failed the review. Please check the reasons and apply again!");
        } catch (Exception e) {
            return error(e.getMessage());
        }
        // return
        return success();
    }

    private void sendMsg() {

    }
}
