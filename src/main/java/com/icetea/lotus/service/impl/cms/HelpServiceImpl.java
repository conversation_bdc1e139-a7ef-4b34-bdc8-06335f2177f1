package com.icetea.lotus.service.impl.cms;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.SysHelp;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.SysHelpService;
import com.icetea.lotus.service.cms.HelpService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Help service.
 */
@Service
@RequiredArgsConstructor
public class HelpServiceImpl extends BaseController implements HelpService {

    private final SysHelpService sysHelpService;
    private final LocaleMessageSourceService msService;

    @Override
    public MessageResult create(SysHelp sysHelp, BindingResult bindingResult) {
        // validate binding
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        // update time
        sysHelp.setCreateTime(DateUtil.getCurrentDate());
        sysHelp = sysHelpService.save(sysHelp);
        return success(sysHelp);
    }

    @Override
    public MessageResult all() {
        // execute query get all data
        List<SysHelp> sysHelps = sysHelpService.findAll();
        if (sysHelps != null && sysHelps.size() > 0) {
            return success(sysHelps);
        }
        return error("data null");
    }

    @Override
    public MessageResult toTop(long id) {
        // find by id
        SysHelp help = sysHelpService.findOne(id);
        // get max sort
        int a = sysHelpService.getMaxSort();
        // set data
        help.setSort(a + 1);
        help.setIsTop("0");
        //save db
        sysHelpService.save(help);
        return success(msService.getMessage("TOP_SUCCESS"));
    }

    @Override
    public MessageResult toDown(long id) {
        // find by id
        SysHelp help = sysHelpService.findOne(id);
        // set value
        help.setIsTop("1");
        sysHelpService.save(help);
        return success();
    }

    @Override
    public MessageResult detail(Long id) {
        // find by id
        SysHelp sysHelp = sysHelpService.findOne(id);
        notNull(sysHelp, "validate id!");
        return success(sysHelp);
    }

    @Override
    public MessageResult update(SysHelp sysHelp, BindingResult bindingResult) {
        //validate id
        notNull(sysHelp.getId(), "validate id!");
        // validate binding
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        // find by id
        SysHelp one = sysHelpService.findOne(sysHelp.getId());
        notNull(one, "validate id!");
        sysHelpService.save(sysHelp);
        return success();
    }

    @Override
    public MessageResult deleteOne(Long[] ids) {
        // execute query delete id
        sysHelpService.deleteBatch(ids);
        return success();
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
//        List<BooleanExpression> predicates = new ArrayList<>();
//        predicates.add(QBusinessCancelApply.businessCancelApply.status.in(CANCEL_AUTH, RETURN_FAILED, RETURN_SUCCESS));

        // Get all data from SysHelp
        Page<SysHelp> all = sysHelpService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<SysHelp> all = sysHelpService.findAll();
        return new FileUtil<SysHelp>(new LocaleMessageSourceService()).exportExcel(request, response, all, "sysHelp");
    }
}
