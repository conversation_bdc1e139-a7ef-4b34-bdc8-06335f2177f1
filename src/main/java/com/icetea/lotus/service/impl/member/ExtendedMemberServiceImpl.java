package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.DepositStatusEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.MemberDTO;
import com.icetea.lotus.entity.spot.BusinessAuthApply;
import com.icetea.lotus.entity.spot.DepositRecord;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.event.MemberEvent;
import com.icetea.lotus.model.screen.MemberScreen;
import com.icetea.lotus.service.BusinessAuthApplyService;
import com.icetea.lotus.service.DepositRecordService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedMemberService;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import io.micrometer.common.util.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.icetea.lotus.constant.CertifiedBusinessStatus.AUDITING;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.CANCEL_AUTH;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.FAILED;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.VERIFIED;
import static com.icetea.lotus.constant.MemberLevelEnum.IDENTIFICATION;
import static com.icetea.lotus.entity.spot.QMember.member;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

/**
 * The type Extended member service.
 */
@Service
@Slf4j
public class ExtendedMemberServiceImpl extends BaseAdminController implements ExtendedMemberService {

    private final MemberService memberService;

    private final MemberWalletService memberWalletService;

    private final BusinessAuthApplyService businessAuthApplyService;

    private final DepositRecordService depositRecordService;

    private final LocaleMessageSourceService messageSource;

    private final MemberEvent memberEvent;

    public ExtendedMemberServiceImpl(BaseAdminService baseAdminService, MemberService memberService, MemberWalletService memberWalletService, BusinessAuthApplyService businessAuthApplyService, DepositRecordService depositRecordService, LocaleMessageSourceService messageSource, MemberEvent memberEvent) {
        super(baseAdminService);
        this.memberService = memberService;
        this.memberWalletService = memberWalletService;
        this.businessAuthApplyService = businessAuthApplyService;
        this.depositRecordService = depositRecordService;
        this.messageSource = messageSource;
        this.memberEvent = memberEvent;
    }

    @Override
    public MessageResult all() {
        List<Member> all = memberService.findAll();
        if (all != null && !all.isEmpty()) {
            return success(all);
        }
        return error(messageSource.getMessage("REQUEST_FAILED"));
    }

    @Override
    public MessageResult detail(Long id) {
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        List<MemberWallet> list = memberWalletService.findAllByMemberId(member.getId());
        MemberDTO memberDTO = new MemberDTO();
        memberDTO.setMember(member);
        memberDTO.setList(list);
        return success(memberDTO);
    }

    @Override
    public MessageResult delete(Long id) {
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        member.setStatus(CommonStatus.ILLEGAL);// Illegal modification of status
        memberService.save(member);
        return success();
    }

    @Override
    public MessageResult update(Member member) {
        if (member.getId() == null) {
            return error("id must be passed");
        }
        Member one = memberService.findOne(member.getId());
        if (one == null) {
            return error("The user does not exist");
        }
        if (StringUtils.isNotBlank(member.getUsername())) {
            one.setUsername(member.getUsername());
        }
        if (StringUtils.isNotBlank(member.getPassword())) {
            one.setPassword(member.getPassword());
        }
        if (StringUtils.isNotBlank(member.getRealName())) {
            one.setRealName(member.getRealName());
        }
        Member save = memberService.save(one);
        return success(save);
    }

    @Override
    public MessageResult auditBusiness(Long id, CertifiedBusinessStatus status, String detail) {
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        // Confirmation is under review
        isTrue(member.getCertifiedBusinessStatus() == AUDITING, "validate member certifiedBusinessStatus!");
        // Confirm that the value of incoming certifiedBusinessStatus is correct, the review is passed or not passed
        isTrue(status == VERIFIED || status == FAILED, "validate certifiedBusinessStatus!");
        // member.setCertifiedBusinessApplyTime(new Date());//time
        List<BusinessAuthApply> businessAuthApplyList = businessAuthApplyService.findByMemberAndCertifiedBusinessStatus(member, AUDITING);
        if (status == VERIFIED) {
            // pass
            member.setCertifiedBusinessStatus(VERIFIED);// Certified
            member.setMemberLevel(IDENTIFICATION);// Certified merchant
            if (businessAuthApplyList != null && businessAuthApplyList.size() > 0) {
                BusinessAuthApply businessAuthApply = businessAuthApplyList.get(0);
                businessAuthApply.setCertifiedBusinessStatus(VERIFIED);
                // If you choose a margin strategy when applying
                if (businessAuthApply.getBusinessAuthDeposit() != null) {
                    // Deduct margin
                    MemberWallet memberWallet = memberWalletService.findByCoinUnitAndMemberId(businessAuthApply.getBusinessAuthDeposit().getCoin().getUnit(), member.getId());
                    memberWallet.setFrozenBalance(memberWallet.getFrozenBalance().subtract(businessAuthApply.getAmount()));
                    DepositRecord depositRecord = new DepositRecord();
                    depositRecord.setId(UUID.randomUUID().toString());
                    depositRecord.setAmount(businessAuthApply.getAmount());
                    depositRecord.setCoin(businessAuthApply.getBusinessAuthDeposit().getCoin());
                    depositRecord.setMember(member);
//                  TODO  depositRecord.setStatus(DepositStatusEnum.PAY);
                    depositRecordService.create(depositRecord);
                    businessAuthApply.setDepositRecordId(depositRecord.getId());
                }
            }
        } else {
            // Not passed
            member.setCertifiedBusinessStatus(FAILED);// Authentication failed
            if (businessAuthApplyList != null && businessAuthApplyList.size() > 0) {
                BusinessAuthApply businessAuthApply = businessAuthApplyList.get(0);
                businessAuthApply.setCertifiedBusinessStatus(FAILED);
                businessAuthApply.setDetail(detail);
                // Refund the amount frozen when applying for merchant certification
                if (businessAuthApply.getBusinessAuthDeposit() != null) {
                    MemberWallet memberWallet = memberWalletService.findByCoinUnitAndMemberId(businessAuthApply.getBusinessAuthDeposit().getCoin().getUnit(), member.getId());
                    memberWallet.setFrozenBalance(memberWallet.getFrozenBalance().subtract(businessAuthApply.getAmount()));
                    memberWallet.setBalance(memberWallet.getBalance().add(businessAuthApply.getAmount()));
                }
            }
        }
        member.setCertifiedBusinessCheckTime(new Date());
        memberService.save(member);
        return success();
    }

    @Override
    public MessageResult page(PageModel pageModel, MemberScreen screen) {
        Predicate predicate = getPredicate(screen);
        Page<Member> all = memberService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult getBusinessAuthApply(Long id, CertifiedBusinessStatus status) {
        if (status == null) {
            return MessageResult.error("Missing parameters");
        }
        isTrue(status == AUDITING || status == CANCEL_AUTH, "validate certifiedBusinessStatus!");
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        // Query application records
        List<BusinessAuthApply> businessAuthApplyList = businessAuthApplyService.findByMemberAndCertifiedBusinessStatus(member, status);
        MessageResult result = MessageResult.success();
        if (businessAuthApplyList != null && businessAuthApplyList.size() > 0) {
            result.setData(businessAuthApplyList.get(0));
        }
        return result;
    }

    private Long coverToLong(String str){
        try {
            return Long.valueOf(str);// If it is a number,
        } catch (Exception e) {
            return null;// If an exception is thrown, return null
        }
    }

    private Predicate getPredicate(MemberScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (screen.getStatus() != null) {
            booleanExpressions.add(member.certifiedBusinessStatus.eq(screen.getStatus()));
        }
        if (screen.getStartTime() != null) {
            booleanExpressions.add(member.registrationTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(screen.getEndTime());
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            booleanExpressions.add(member.registrationTime.lt(calendar.getTime()));
        }

        if (!StringUtils.isEmpty(screen.getAccount())) {
            Long accountId = coverToLong(screen.getAccount());
            if(accountId==null) {
                booleanExpressions.add(member.username.like("%" + screen.getAccount() + "%")
                        .or(member.mobilePhone.like(screen.getAccount() + "%"))
                        .or(member.email.like(screen.getAccount() + "%"))
                        .or(member.realName.like("%" + screen.getAccount() + "%")));
            }else {
                booleanExpressions.add(member.username.like("%" + screen.getAccount() + "%")
                        .or(member.mobilePhone.like(screen.getAccount() + "%"))
                        .or(member.email.like(screen.getAccount() + "%"))
                        .or(member.id.eq(accountId))
                        .or(member.realName.like("%" + screen.getAccount() + "%")));
            }
        }
        if (screen.getCommonStatus() != null) {
            booleanExpressions.add(member.status.eq(screen.getCommonStatus()));
        }

        if(screen.getSuperPartner() != null && !screen.getSuperPartner().equals("")) {
            booleanExpressions.add(member.superPartner.eq(screen.getSuperPartner()));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }

    private Predicate getPredicate(MemberScreen screen, Long userId) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (screen.getStatus() != null) {
            booleanExpressions.add(member.certifiedBusinessStatus.eq(screen.getStatus()));
        }
        if (screen.getStartTime() != null) {
            booleanExpressions.add(member.registrationTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(screen.getEndTime());
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            booleanExpressions.add(member.registrationTime.lt(calendar.getTime()));
        }

        if (!StringUtils.isEmpty(screen.getAccount())) {
            booleanExpressions.add(member.username.like("%" + screen.getAccount() + "%")
                    .or(member.mobilePhone.like(screen.getAccount() + "%"))
                    .or(member.email.like(screen.getAccount() + "%"))
                    .or(member.id.eq(Long.valueOf(screen.getAccount())))
                    .or(member.realName.like("%" + screen.getAccount() + "%")));
        }
        // Filter out the people I invited
        booleanExpressions.add(member.inviterId.eq(userId));
        if (screen.getCommonStatus() != null) {
            booleanExpressions.add(member.status.eq(screen.getCommonStatus()));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }

    @Override
    public MessageResult outExcel(Date startTime, Date endTime, String account, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<BooleanExpression> booleanExpressionList = getBooleanExpressionList(startTime, endTime, account, null);
        List<Member> list = memberService.queryWhereOrPage(booleanExpressionList, null, null).getContent();
        return new FileUtil<Member>(messageSource).exportExcel(request, response, list, "member");
    }

    // Obtaining conditions
    private List<BooleanExpression> getBooleanExpressionList(
            Date startTime, Date endTime, String account, CertifiedBusinessStatus status) {
        List<BooleanExpression> booleanExpressionList = new ArrayList();
        if (status != null) {
            booleanExpressionList.add(member.certifiedBusinessStatus.eq(status));
        }
        if (startTime != null) {
            booleanExpressionList.add(member.registrationTime.gt(startTime));
        }
        if (endTime != null) {
            booleanExpressionList.add(member.registrationTime.lt(endTime));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(account)) {
            booleanExpressionList.add(member.username.like("%" + account + "%")
                    .or(member.mobilePhone.like(account + "%"))
                    .or(member.email.like(account + "%")));
        }
        return booleanExpressionList;
    }

    @Override
    public MessageResult publishAdvertise(Long memberId, BooleanEnum status) {
        Member member = memberService.findOne(memberId);
        if (member.getCertifiedBusinessStatus() != CertifiedBusinessStatus.VERIFIED) {
            return error("Please verify the merchant first");
        }
        Assert.notNull(member, "The player does not exist");
        member.setPublishAdvertise(status);
        memberService.save(member);
        return success(status == BooleanEnum.IS_FALSE ? "Prohibited ads successfully" : "Release of ban successfully");
    }

    @Override
    public MessageResult ban(CommonStatus status, Long memberId) {
        Member member = memberService.findOne(memberId);
        member.setStatus(status);
        memberService.save(member);
        log.info(">>>>>>>>>>>>>>>>>>>>> Start initializing BZB data >>>>>>>");
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult alterTransactionStatus(BooleanEnum status, Long memberId) {
        Member member = memberService.findOne(memberId);
        member.setTransactionStatus(status);
        memberService.save(member);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult alterSuperPartner(String superPartner, Long memberId) {
        Member member = memberService.findOne(memberId);
        member.setSuperPartner(superPartner);
        memberService.save(member);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult pageSuperPartner(PageModel pageModel, MemberScreen screen) {
        screen.setSuperPartner("1"); // Default selection of agent
        Predicate predicate = getPredicate(screen);
        Page<Member> all = memberService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult pageSuperMember(PageModel pageModel, MemberScreen screen, Long userId) {
        // Check if the user is an agent
        Member checkMember = memberService.findOne(userId);
        if(!checkMember.getSuperPartner().equals("1")) {
            return error("You are not an agent!");
        }
        Predicate predicate = getPredicate(screen, userId);
        Page<Member> all = memberService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult setInviter(Long id, Long inviterId) throws InterruptedException {
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        Member pMember = memberService.findOne(inviterId);
        notNull(member, "validate id!");
        if(member.getInviterId()!=null){
            return error("An invitation already exists");
        }
        memberEvent.setMemberInviter(member,pMember);
        return success();
    }


}
