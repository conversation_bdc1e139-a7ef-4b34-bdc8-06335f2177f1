package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.AnnouncementClassification;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Announcement;
import com.icetea.lotus.entity.spot.QAnnouncement;
import com.icetea.lotus.service.AnnouncementService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedAnnouncementService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;

/**
 * The type Extended announcement service.
 */
@Slf4j
@Service
public class ExtendedAnnouncementServiceImpl extends BaseAdminController implements ExtendedAnnouncementService {

    private final AnnouncementService announcementService;
    private final LocaleMessageSourceService messageSource;

    public ExtendedAnnouncementServiceImpl(BaseAdminService baseAdminService, AnnouncementService announcementService, LocaleMessageSourceService messageSource) {
        super(baseAdminService);
        this.announcementService = announcementService;
        this.messageSource = messageSource;
    }

    @Override
    public MessageResult create(String title, String content, String lang, AnnouncementClassification announcementClassification, Boolean isShow, String imgUrl) {
        Announcement announcement = new Announcement();
        announcement.setTitle(title);
        announcement.setContent(content);
        announcement.setIsShow(isShow);
        announcement.setLang(lang);
        announcement.setAnnouncementClassification(announcementClassification);
        announcement.setImgUrl(imgUrl);
        announcementService.save(announcement);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult toTop(long id) {
        Announcement announcement = (Announcement) announcementService.findById(id);
        int a = announcementService.getMaxSort();
        announcement.setSort(a+1);
        announcement.setIsTop("0");
        announcementService.save(announcement);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult toDown(long id) {
        Announcement announcement = (Announcement) announcementService.findById(id);
        announcement.setIsTop("1");
        announcementService.save(announcement);
        return success();
    }

    @Override
    public MessageResult page(PageModel pageModel, Boolean isShow) {
        // condition
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (isShow != null) {
            booleanExpressions.add(QAnnouncement.announcement.isShow.eq(isShow));
        }
        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<Announcement> all = announcementService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult deleteOne(Long[] ids) {
        announcementService.deleteBatch(ids);
        return success();
    }

    @Override
    public MessageResult detail(Long id) {
        Announcement announcement = announcementService.findById(id);
        Assert.notNull(announcement, "validate id!");
        return success(announcement);
    }

    @Override
    public MessageResult turnOn(Long id) {
        Announcement announcement = announcementService.findById(id);
        Assert.notNull(announcement, "validate id!");
        announcement.setIsShow(true);
        announcementService.save(announcement);
        return success();
    }

    @Override
    public MessageResult turnOff(Long id) {
        Announcement announcement = announcementService.findById(id);
        Assert.notNull(announcement, "validate id!");
        announcement.setIsShow(false);
        announcementService.save(announcement);
        return success();
    }

    @Override
    public MessageResult update(Long id, String title, String content, Boolean isShow, String lang, AnnouncementClassification announcementClassification, String imgUrl) {
        Announcement announcement = announcementService.findById(id);
        Assert.notNull(announcement, "validate id!");
        announcement.setTitle(title);
        announcement.setContent(content);
        announcement.setIsShow(isShow);
        announcement.setLang(lang);
        announcement.setAnnouncementClassification(announcementClassification);
        announcement.setImgUrl(imgUrl);
        announcementService.save(announcement);
        return success();
    }


}
