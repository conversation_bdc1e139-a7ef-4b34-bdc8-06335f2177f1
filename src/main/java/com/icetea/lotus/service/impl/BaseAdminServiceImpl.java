package com.icetea.lotus.service.impl;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.convert.ConvertService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * The type Base admin service.
 */
@Service
@RequiredArgsConstructor
public class BaseAdminServiceImpl extends BaseController implements BaseAdminService {

    private final RedisTemplate redisTemplate;

    private final LocaleMessageSourceService msService;

    @Override
    public Admin getAdmin(HttpServletRequest request) {
        HttpSession session = request.getSession();
        return (Admin) session.getAttribute(SysConstant.SESSION_ADMIN);
    }

    @Override
    public MessageResult checkCode(String code, String key) {
        return success(msService.getMessage("CODE_CORRECT"));
        //        ValueOperations valueOperations = redisTemplate.opsForValue();
//        Object value = valueOperations.get(key);
//        if(value==null) {
//            return error(msService.getMessage("CODE_NOT_EXIST_RESEND"));
//        }
//        if(!value.toString().equals(code)) {
//            return  error(msService.getMessage("CODE_ERROR"));
//        }
//        valueOperations.getOperations().delete(key);
//        /**
//         * No need to verify again within ten minutes
//         */
//        valueOperations.set(key+"_PASS",true,10, TimeUnit.MINUTES);
//        return success(msService.getMessage("CODE_CORRECT"));
    }
}
