package com.icetea.lotus.service.impl.promotion;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.PromotionRewardType;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.QMember;
import com.icetea.lotus.entity.spot.RewardPromotionSetting;
import com.icetea.lotus.model.MemberPromotionScreen;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberPromotionService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.RewardPromotionSettingService;
import com.icetea.lotus.service.promotion.ExtendedMemberPromotionService;
import com.icetea.lotus.util.BigDecimalUtils;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.icetea.lotus.vo.PromotionMemberVO;
import com.icetea.lotus.vo.RegisterPromotionVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Extended member promotion service.
 */
@Service
@RequiredArgsConstructor
public class ExtendedMemberPromotionServiceImpl implements ExtendedMemberPromotionService {

    private final MemberService memberService ;

    private final MemberPromotionService memberPromotionService ;

    private final RewardPromotionSettingService rewardPromotionSettingService ;

    private final LocaleMessageSourceService messageSource;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MessageResult page(PageModel pageModel, MemberPromotionScreen screen) {
        Map<String,Object> map = getMemberPromotions(pageModel,screen);
        PageImpl<Object> pageImpl = new PageImpl<>((List<Object>)map.get("list"),pageModel.getPageable(),(long)map.get("total"));
        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"),pageImpl);
    }

    @Override
    public MessageResult promotionDetails(PageModel pageModel, Long memberId) {
        pageModel.setSort();
        Page<RegisterPromotionVO> page = memberPromotionService.getPromotionDetails(memberId,pageModel);
        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"),page);
    }

    @Override
    public void outExcel(PageModel pageModel, MemberPromotionScreen screen, HttpServletResponse response) throws IOException {
        Map<String,Object> map = getMemberPromotions(pageModel,screen);
        ExcelUtil.listToExcel((List<PromotionMemberVO>)map.get("list"),PromotionMemberVO.class.getDeclaredFields(),response.getOutputStream());
    }

    private Map getMemberPromotions(PageModel pageModel, MemberPromotionScreen screen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (!StringUtils.isEmpty(screen.getAccount())) {
            booleanExpressions.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%"))
                    .or(QMember.member.mobilePhone.like(screen.getAccount() + "%"))
                    .or(QMember.member.email.like(screen.getAccount() + "%")));
        }
        if (screen.getMinPromotionNum() != -1) {
            booleanExpressions.add(QMember.member.firstLevel.add(QMember.member.secondLevel).goe(screen.getMinPromotionNum()));
        }

        if (screen.getMaxPromotionNum() != -1) {
            booleanExpressions.add(QMember.member.firstLevel.add(QMember.member.secondLevel).loe(screen.getMaxPromotionNum()));
        }

        RewardPromotionSetting setting = rewardPromotionSettingService.findByType(PromotionRewardType.REGISTER);

        Assert.notNull(setting, "Register Reward Configuration null");

        String info = setting.getInfo();

        JsonNode jsonNode;
        try {
            jsonNode = objectMapper.readTree(info);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON", e);
        }

        BigDecimal one = new BigDecimal(jsonNode.get("one").asText());

        BigDecimal two = new BigDecimal(jsonNode.get("two").asText());

        Map<String, String> map = new HashMap<>();

        Page<Member> page = memberService.findAll(PredicateUtils.getPredicate(booleanExpressions), pageModel.getPageable());

        List<PromotionMemberVO> list = page.getContent().stream().map(x -> PromotionMemberVO.builder().id(x.getId())
                        .username(x.getUsername())
                        .email(x.getEmail())
                        .mobilePhone(x.getMobilePhone())
                        .realName(x.getRealName())
                        .promotionCode(x.getPromotionCode())
                        .promotionNum(x.getFirstLevel() + x.getSecondLevel())
                        .reward(map.put(setting.getCoin().getName(),
                                BigDecimalUtils.mul(one, new BigDecimal(x.getFirstLevel() + "").add(
                                        BigDecimalUtils.mul(two, new BigDecimal(x.getSecondLevel() + ""))
                                )).toString()) != null ? map : null)
                        .build())
                .collect(Collectors.toList());
        Map<String, Object> map1 = new HashMap();
        map1.put("total", page.getTotalElements());
        map1.put("list", list);
        return map1;
    }
}
