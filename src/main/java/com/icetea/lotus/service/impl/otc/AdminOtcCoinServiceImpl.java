package com.icetea.lotus.service.impl.otc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.OtcCoin;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.OtcCoinService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.otc.AdminOtcCoinService;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Admin otc coin service.
 */
@Service
public class AdminOtcCoinServiceImpl extends BaseAdminController implements AdminOtcCoinService {

    private final OtcCoinService otcCoinService;

    private final LocaleMessageSourceService messageSource;

    private final CoinService coinService ;

    public AdminOtcCoinServiceImpl(BaseAdminService baseAdminService, OtcCoinService otcCoinService, LocaleMessageSourceService messageSource, CoinService coinService) {
        super(baseAdminService);
        this.otcCoinService = otcCoinService;
        this.messageSource = messageSource;
        this.coinService = coinService;
    }

    @Override
    public MessageResult create(OtcCoin otcCoin) {
        Coin coin = coinService.findByUnit(otcCoin.getUnit());
        if(coin==null) {
            return error(messageSource.getMessage("COIN_NOT_SUPPORTED"));
        }
        otcCoinService.save(otcCoin);
        return success();
    }

    @Override
    public MessageResult all() {
        List<OtcCoin> all = otcCoinService.findAll();
        if (all != null && all.size() > 0) {
            return success(all);
        }
        return error(messageSource.getMessage("NO_DATA"));
    }

    @Override
    public MessageResult detail(Long id) {
        OtcCoin one = otcCoinService.findOne(id);
        notNull(one, "validate otcCoin.id!");
        return success(one);
    }

    @Override
    public MessageResult update(OtcCoin otcCoin) {
        OtcCoin one = otcCoinService.findOne(otcCoin.getId());
        notNull(one, "validate otcCoin.id!");
        otcCoinService.save(otcCoin);
        return success();
    }

    @Override
    public MessageResult deletes(Long[] ids) {
        otcCoinService.deletes(ids);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
        Page<OtcCoin> pageResult = otcCoinService.findAll(null, pageModel.getPageable());
        return success(pageResult);
    }

    @Override
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<OtcCoin> all = otcCoinService.findAll();
        return new FileUtil<OtcCoin>(messageSource).exportExcel(request, response, all, "otcCoin");
    }

    @Override
    public MessageResult getAllOtcCoinUnits() {
        List<String> list = otcCoinService.findAllUnits() ;
        return success(messageSource.getMessage("SUCCESS"),list);
    }

    @Override
    public MessageResult memberStatistics(Long id, BigDecimal jyRate) {
        OtcCoin one = otcCoinService.findOne(id);
        notNull(one, "validate otcCoin.id");
        one.setJyRate(jyRate);
        otcCoinService.save(one);
        return success();
    }
}
