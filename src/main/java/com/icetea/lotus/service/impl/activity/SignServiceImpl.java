package com.icetea.lotus.service.impl.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SignStatus;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Sign;
import com.icetea.lotus.model.create.SignCreate;
import com.icetea.lotus.model.screen.SignScreen;
import com.icetea.lotus.model.update.SignUpdate;
import com.icetea.lotus.model.vo.SignVO;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.SignService;
import com.icetea.lotus.service.activity.SignedService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;

/**
 * The type Sign service.
 */
@Service
@RequiredArgsConstructor
public class SignServiceImpl extends BaseController implements SignedService {

    private final SignService service;

    private final CoinService coinService;

    @Override
    public MessageResult create(SignCreate model, BindingResult bindingResult) {
        //validate
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        //save to db
        service.save(model.transformation(service, coinService));
        return success();
    }

    @Override
    public MessageResult update(SignUpdate model, BindingResult bindingResult, Long id) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        // Verify id
        Sign sign = service.findById(id);
        Assert.notNull(sign, "validate id!");
        // Conversion
        sign = model.transformation(coinService, sign);
        // keep
        service.save(sign);
        return success();
    }

    @Override
    public MessageResult pageQuery(SignScreen screen, PageModel pageModel) {
        Page<Sign> source = service.findAllScreen(screen, pageModel);
        Page<SignVO> page = source.map(x -> SignVO.getSignVO(x));
        return success(page);
    }

    @Override
    public MessageResult detail(Long id) {
        // Verify id
        Sign sign = service.findById(id);
        Assert.notNull(sign, "validate id!");
        // map data
        SignVO signVO = SignVO.getSignVO(sign);
        return success(signVO);
    }

    @Override
    public MessageResult earlyClosing(Long id) {
        // Verify id
        Sign sign = service.findById(id);
        Assert.notNull(sign, "validate id!");
        Assert.isTrue(sign.getStatus() == SignStatus.UNDERWAY, "validate status!");
        //set status = FINISH
        service.earlyClosing(sign);
        return success();
    }

    @Override
    public MessageResult is() {
        // excute query to check if have value
        Sign sign = service.fetchUnderway();
        if (sign != null) {
            return success(true);
        } else {
            return success(false);
        }
    }
}
