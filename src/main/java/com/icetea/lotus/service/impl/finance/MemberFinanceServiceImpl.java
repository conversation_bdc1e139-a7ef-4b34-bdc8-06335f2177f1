package com.icetea.lotus.service.impl.finance;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.QMember;
import com.icetea.lotus.entity.spot.QMemberDeposit;
import com.icetea.lotus.entity.spot.QMemberTransaction;
import com.icetea.lotus.es.ESUtils;
import com.icetea.lotus.model.screen.MemberDepositScreen;
import com.icetea.lotus.model.screen.MemberTransactionScreen;
import com.icetea.lotus.model.vo.MemberTransaction2ESVO;
import com.icetea.lotus.model.vo.MemberTransactionExcelVO;
import com.icetea.lotus.model.vo.MemberTransactionFeeExcelVO;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberDepositService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.MemberFinanceService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.MemberDepositVO;
import com.icetea.lotus.vo.MemberTransactionVO;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Member finance service.
 */
@Service
@Slf4j
public class MemberFinanceServiceImpl extends BaseAdminController implements MemberFinanceService {

    private final MemberDepositService memberDepositService;
    private final LocaleMessageSourceService messageSource;
    private final RestTemplate restTemplate;
    private final EntityManager entityManager;
    private final JPAQueryFactory queryFactory;
    private final MemberTransactionService memberTransactionService;
    private final ESUtils esUtils;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Logger logger = LoggerFactory.getLogger(BaseAdminController.class);

    public MemberFinanceServiceImpl(BaseAdminService baseAdminService, MemberDepositService memberDepositService, LocaleMessageSourceService messageSource, RestTemplate restTemplate, EntityManager entityManager, JPAQueryFactory queryFactory, MemberTransactionService memberTransactionService, ESUtils esUtils) {
        super(baseAdminService);
        this.memberDepositService = memberDepositService;
        this.messageSource = messageSource;
        this.restTemplate = restTemplate;
        this.entityManager = entityManager;
        this.queryFactory = queryFactory;
        this.memberTransactionService = memberTransactionService;
        this.esUtils = esUtils;
    }

    @Override
    public MessageResult page(PageModel pageModel, MemberDepositScreen screen) {
        List<BooleanExpression> predicates = new ArrayList<>();
        predicates.add(QMember.member.id.eq(QMemberDeposit.memberDeposit.memberId));
        if (!StringUtils.isEmpty(screen.getUnit())) {
            predicates.add((QMemberDeposit.memberDeposit.unit.equalsIgnoreCase(screen.getUnit())));
        }
        if (!StringUtils.isEmpty(screen.getAddress())) {
            predicates.add((QMemberDeposit.memberDeposit.address.eq(screen.getAddress())));
        }
        if (!StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }
        Page<MemberDepositVO> page = memberDepositService.page(predicates, pageModel);

        List<MemberDepositVO> list = page.getContent();
        for (MemberDepositVO item : list) {
            if (!StringUtils.isEmpty(item.getAddress())) {
                String url = "http://SERVICE-RPC-" + item.getUnit() + "/rpc/balance/" + item.getAddress();
                item.setWalletBalance(getRPCWalletBalance(url, item.getUnit()));
            } else {
                item.setWalletBalance(BigDecimal.ZERO);
            }
        }
        return success(messageSource.getMessage("SUCCESS"), page);
    }

    private BigDecimal getRPCWalletBalance(String url, String unit) {
        try {
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String balance = mr.getData().toString();
                    BigDecimal bigDecimal = new BigDecimal(balance);
                    log.info(unit + "(" + url + ")" + messageSource.getMessage("HOT_WALLET_BALANCE"), bigDecimal);
                    return bigDecimal;
                }
            }
        } catch (Exception e) {
            log.error("error={}", e.toString());
            return new BigDecimal("0");
        }
        return new BigDecimal("0");
    }

    @Override
    public MessageResult pageSuperDeposit(PageModel pageModel, MemberDepositScreen screen, Long memberId) {
        List<BooleanExpression> predicates = new ArrayList<>();
        predicates.add(QMember.member.id.eq(QMemberDeposit.memberDeposit.memberId)); // Conjunction table query conditions
        predicates.add(QMember.member.inviterId.eq(memberId)); // Conjunction table query conditions

        if (!StringUtils.isEmpty(screen.getUnit())) {
            predicates.add((QMemberDeposit.memberDeposit.unit.equalsIgnoreCase(screen.getUnit())));
        }
        if (!StringUtils.isEmpty(screen.getAddress())) {
            predicates.add((QMemberDeposit.memberDeposit.address.eq(screen.getAddress())));
        }
        if (!StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }
        Page<MemberDepositVO> page = memberDepositService.page(predicates, pageModel);

        List<MemberDepositVO> list = page.getContent();

        return success(messageSource.getMessage("SUCCESS"), page);
    }

    @Override
    public MessageResult all() {
        List<MemberTransaction> memberTransactionList = memberTransactionService.findAll();
        if (memberTransactionList != null && !memberTransactionList.isEmpty()) {
            return success(memberTransactionList);
        }
        return error(messageSource.getMessage("NO_DATA"));
    }

    @Override
    public MessageResult detail(Long id) {
        MemberTransaction memberTransaction = memberTransactionService.findOne(id);
        notNull(memberTransaction, "validate id!");
        return success(memberTransaction);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, MemberTransactionScreen screen, HttpServletResponse response) throws IOException {
        List<Predicate> predicates = new ArrayList<>();

        if (screen.getMemberId() != null) {
            predicates.add((QMember.member.id.eq(screen.getMemberId())));
        }
        if (!StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }
        if (screen.getStartTime() != null) {
            predicates.add(QMemberTransaction.memberTransaction.createTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null) {
            predicates.add(QMemberTransaction.memberTransaction.createTime.lt(DateUtil.dateAddDay(screen.getEndTime(), 1)));
        }
        if (screen.getType() != null) {
            predicates.add(QMemberTransaction.memberTransaction.type.eq(screen.getType()));
        }

        if (screen.getMinMoney() != null) {
            predicates.add(QMemberTransaction.memberTransaction.amount.goe(screen.getMinMoney()));
        }

        if (screen.getMaxMoney() != null) {
            predicates.add(QMemberTransaction.memberTransaction.amount.loe(screen.getMaxMoney()));
        }

        if (screen.getMinFee() != null) {
            predicates.add(QMemberTransaction.memberTransaction.fee.goe(screen.getMinFee()));
        }

        if (screen.getMaxFee() != null) {
            predicates.add(QMemberTransaction.memberTransaction.fee.loe(screen.getMaxFee()));
        }
        if (screen.getSymbol() != null) {
            predicates.add(QMemberTransaction.memberTransaction.symbol.like(screen.getSymbol()));
        }
        if (screen.getIsOut() != null && screen.getIsOut() == 1) {
            predicates.add(QMemberTransaction.memberTransaction.memberId.notIn(1));
            List<MemberTransactionVO> memberTransactionVOS = memberTransactionService.joinFindAll(predicates, pageModel);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (screen.getOutType() != null && screen.getOutType() == 0) {
                List<MemberTransactionExcelVO> voList = new ArrayList<>();
                for (MemberTransactionVO v : memberTransactionVOS) {
                    MemberTransactionExcelVO vo = new MemberTransactionExcelVO();
                    vo.setMemberId(v.getMemberId());
                    vo.setAmount(v.getAmount().toPlainString());
                    vo.setFee(v.getFee().toPlainString());
                    vo.setCreateTime(sdf.format(v.getCreateTime()));
                    TransactionType type = v.getType();
                    vo.setType(type.getName());
                    voList.add(vo);
                }
                if (voList.size() == 0) {
                    MemberTransactionExcelVO vo = new MemberTransactionExcelVO();
                    voList.add(vo);
                }
                ExcelUtil.listToExcel(voList, MemberTransactionExcelVO.class.getDeclaredFields(), response.getOutputStream());
            } else {
                List<MemberTransactionFeeExcelVO> voList = new ArrayList<>();
                for (MemberTransactionVO v : memberTransactionVOS) {
                    MemberTransactionFeeExcelVO vo = new MemberTransactionFeeExcelVO();
                    vo.setMemberId(v.getMemberId());
                    vo.setSymbol(v.getSymbol());
                    vo.setFee(v.getFee().toPlainString());
                    vo.setCreateTime(sdf.format(v.getCreateTime()));
                    TransactionType type = v.getType();
                    vo.setType(type.getName());
                    voList.add(vo);
                }
                if (voList.size() == 0) {
                    MemberTransactionFeeExcelVO vo = new MemberTransactionFeeExcelVO();
                    voList.add(vo);
                }
                ExcelUtil.listToExcel(voList, MemberTransactionFeeExcelVO.class.getDeclaredFields(), response.getOutputStream());
            }
            return null;
        }

        Page<MemberTransactionVO> results = memberTransactionService.joinFind(predicates, pageModel);
        return success(results);
    }

    @Override
    public MessageResult outExcel(Date startTime, Date endTime, TransactionType type, Long memberId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<BooleanExpression> booleanExpressionList = getBooleanExpressionList(startTime, endTime, type, memberId);
        List<MemberTransaction> list = memberTransactionService.queryWhereOrPage(booleanExpressionList, null, null).getContent();
        return new FileUtil<MemberTransaction>(messageSource).exportExcel(request, response, list, "Transaction history");
    }

    // Obtaining conditions
    private List<BooleanExpression> getBooleanExpressionList(
            Date startTime, Date endTime, TransactionType type, Long memberId) {
        QMemberTransaction qEntity = QMemberTransaction.memberTransaction;
        List<BooleanExpression> booleanExpressionList = new ArrayList();
        if (startTime != null) {
            booleanExpressionList.add(qEntity.createTime.gt(startTime));
        }
        if (endTime != null) {
            booleanExpressionList.add(qEntity.createTime.lt(endTime));
        }
        if (type != null) {
            booleanExpressionList.add(qEntity.type.eq(type));
        }
        if (memberId != null) {
            booleanExpressionList.add(qEntity.memberId.eq(memberId));
        }
        return booleanExpressionList;
    }

    @Override
    public MessageResult getPageQueryByES(MemberTransaction2ESVO transactionVO) {
        log.info(">>>>>>>> Inquiry on transaction details>>>>>>>>>>>");
        try {
            String query = "{\"from\":" + (transactionVO.getPageNo() - 1) * transactionVO.getPageSize() + ",\"size\":" + transactionVO.getPageSize() + ",\"sort\":[{\"create_time\":{\"order\":\"desc\"}}]," +
                    "\"query\":{\"bool\":{\"must\":[";
            boolean deleteFlag = false;
            if (!StringUtils.isEmpty(transactionVO.getStartTime()) && !StringUtils.isEmpty(transactionVO.getEndTime())) {
                query += "{\"range\":{\"create_time\":{\"gte\":\"" + transactionVO.getStartTime() + "\",\"lte\":\"" + transactionVO.getEndTime() + "\"}}},";
                deleteFlag = true;
            }
            if (!StringUtils.isEmpty(transactionVO.getType())) {
                query += "{\"match\":{\"type\":\"" + transactionVO.getType() + "\"}},";
                deleteFlag = true;
            }
            if (!StringUtils.isEmpty(transactionVO.getMemberId())) {
                query += "{\"match\":{\"member_id\":\"" + transactionVO.getMemberId() + "\"}},";
                deleteFlag = true;
            }
            if (!StringUtils.isEmpty(transactionVO.getMinMoney())) {
                query += "{\"range\":{\"amount\":{\"gte\":\"" + transactionVO.getMinMoney() + "\"}}},";
                deleteFlag = true;
            }
            if (!StringUtils.isEmpty(transactionVO.getMaxMoney())) {
                query += "{\"range\":{\"amount\":{\"lte\":\"" + transactionVO.getMaxMoney() + "\"}}},";
                deleteFlag = true;
            }
            if (!StringUtils.isEmpty(transactionVO.getMinFee())) {
                query += "{\"range\":{\"fee\":{\"gte\":\"" + transactionVO.getMinFee() + "\"}}},";
            }
            if (!StringUtils.isEmpty(transactionVO.getMaxFee())) {
                query += "{\"range\":{\"fee\":{\"lte\":\"" + transactionVO.getMaxFee() + "\"}}},";
                deleteFlag = true;
            }
            if (deleteFlag) {
                // Remove the last symbol
                query.substring(0, query.length() - 1);
            }
            query += "]}}}";
            try {
                ObjectNode queryNode = (ObjectNode) objectMapper.readTree(query);
                return success(esUtils.queryForAnyOne(queryNode, "member_transaction", "mem_transaction"));
            } catch (JsonProcessingException e) {
                log.error("Error parsing JSON query", e);
                return error("Error parsing JSON query");
            }
        } catch (Exception e) {
            log.info(">>>>>>>Query exceptions>>>>" + e);
            return error("Query exception");
        }
    }

    @Override
    public MessageResult pageQuerySuper(PageModel pageModel, MemberTransactionScreen screen, Long memberId) {
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(QMember.member.inviterId.eq(memberId));

        if (screen.getMemberId() != null) {
            predicates.add((QMember.member.id.eq(screen.getMemberId())));
        }
        if (!StringUtils.isEmpty(screen.getAccount())) {
            predicates.add(QMember.member.username.like("%" + screen.getAccount() + "%")
                    .or(QMember.member.realName.like("%" + screen.getAccount() + "%")));
        }
        if (screen.getStartTime() != null) {
            predicates.add(QMemberTransaction.memberTransaction.createTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null) {
            predicates.add(QMemberTransaction.memberTransaction.createTime.lt(DateUtil.dateAddDay(screen.getEndTime(), 1)));
        }
        if (screen.getType() != null) {
            predicates.add(QMemberTransaction.memberTransaction.type.eq(screen.getType()));
        }

        if (screen.getMinMoney() != null) {
            predicates.add(QMemberTransaction.memberTransaction.amount.goe(screen.getMinMoney()));
        }

        if (screen.getMaxMoney() != null) {
            predicates.add(QMemberTransaction.memberTransaction.amount.loe(screen.getMaxMoney()));
        }

        if (screen.getMinFee() != null) {
            predicates.add(QMemberTransaction.memberTransaction.fee.goe(screen.getMinFee()));
        }

        if (screen.getMaxFee() != null) {
            predicates.add(QMemberTransaction.memberTransaction.fee.loe(screen.getMaxFee()));
        }

        Page<MemberTransactionVO> results = memberTransactionService.joinFind(predicates, pageModel);

        return success(results);
    }
}
