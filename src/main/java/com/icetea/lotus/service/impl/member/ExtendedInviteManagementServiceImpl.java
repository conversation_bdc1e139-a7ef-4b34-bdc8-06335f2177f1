package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberInviteStastic;
import com.icetea.lotus.service.InviteManagementService;
import com.icetea.lotus.service.MemberInviteStasticService;
import com.icetea.lotus.service.member.ExtendedInviteManagementService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.InviteManagementVO;
import com.icetea.lotus.vo.MemberInviteStasticVO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * The type Extended invite management service.
 */
@Service
@RequiredArgsConstructor
public class ExtendedInviteManagementServiceImpl extends BaseController implements ExtendedInviteManagementService {

    private final InviteManagementService inviteManagementService;

    private final MemberInviteStasticService memberInviteStasticService;

    @Override
    public MessageResult lookAll(InviteManagementVO inviteManagementVO) {
        Page<Member> page = inviteManagementService.lookAll(inviteManagementVO);
        return success(page);
    }

    @Override
    public MessageResult queryCondition(InviteManagementVO inviteManagementVO) {
        Page<Member> page = inviteManagementService.queryCondition(inviteManagementVO);
        return success(page);
    }

    @Override
    public MessageResult queryId(InviteManagementVO inviteManagementVO) {
        Page<Member> page = inviteManagementService.queryId(inviteManagementVO);
        return success(page);
    }

    @Override
    public MessageResult queryRankList(MemberInviteStasticVO memberInviteStasticVO) {
        // type: 0 = number of people ranking // type: 1 = commission ranking
        Page<MemberInviteStastic> page = memberInviteStasticService.queryCondition(memberInviteStasticVO);
        List<MemberInviteStastic> list= page.getContent();
        for(MemberInviteStastic item : list) {
            item.setUserIdentify(item.getIsRobot() + "-" + item.getUserIdentify());
        }
        return success(page);
    }

    @Override
    public MessageResult updateRank(Long id, BigDecimal estimatedReward, BigDecimal extraReward, Integer levelOne, Integer levelTwo) {
        MemberInviteStastic detail = memberInviteStasticService.findById(id);
        if(detail == null) {
            return error("This ranking user does not exist");
        }
        if(estimatedReward != null) {
            detail.setEstimatedReward(estimatedReward);
        }
        if(extraReward != null) {
            detail.setExtraReward(extraReward);
        }
        if(levelOne != null) {
            detail.setLevelOne(levelOne);
        }
        if(levelTwo != null) {
            detail.setLevelTwo(levelTwo);
        }

        memberInviteStasticService.save(detail);

        return success(detail);
    }

    @Override
    public MessageResult updateRank(Long id) {
        MemberInviteStastic detail = memberInviteStasticService.findById(id);

        return success(detail);
    }
}
