package com.icetea.lotus.service.impl.member;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.MemberWalletDTO;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QMember;
import com.icetea.lotus.entity.spot.QMemberWallet;
import com.icetea.lotus.es.ESUtils;
import com.icetea.lotus.model.screen.MemberWalletScreen;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedMemberWalletService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.Predicate;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Extended member wallet service.
 */
@Service
@Slf4j
public class ExtendedMemberWalletServiceImpl extends BaseAdminController implements ExtendedMemberWalletService {

    private final MemberWalletService memberWalletService;
    private final MemberService memberService;
    private final CoinService coinService;
    private final KafkaTemplate kafkaTemplate;
    private final MemberTransactionService memberTransactionService;
    private final LocaleMessageSourceService messageSource;
    private final ESUtils esUtils;
    private final JavaMailSender javaMailSender;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;

    @Value("${spark.system.admins}")
    private String admins;

    public ExtendedMemberWalletServiceImpl(BaseAdminService baseAdminService, MemberWalletService memberWalletService, MemberService memberService, CoinService coinService, KafkaTemplate kafkaTemplate, MemberTransactionService memberTransactionService, LocaleMessageSourceService messageSource, ESUtils esUtils, JavaMailSender javaMailSender) {
        super(baseAdminService);
        this.memberWalletService = memberWalletService;
        this.memberService = memberService;
        this.coinService = coinService;
        this.kafkaTemplate = kafkaTemplate;
        this.memberTransactionService = memberTransactionService;
        this.messageSource = messageSource;
        this.esUtils = esUtils;
        this.javaMailSender = javaMailSender;
    }


    @Override
    public MessageResult getBalance(PageModel pageModel, MemberWalletScreen screen) {
        QMemberWallet qMemberWallet = QMemberWallet.memberWallet;
        QMember qMember = QMember.member;
// QMemberAddress qMemberAddress = QMemberAddress.memberAddress;
        List<Predicate> criteria = new ArrayList<>();
        if (StringUtils.hasText(screen.getAccount())) {
            criteria.add(qMember.username.like("%" + screen.getAccount() + "%")
                    .or(qMember.mobilePhone.like(screen.getAccount() + "%"))
                    .or(qMember.email.like(screen.getAccount() + "%"))
                    .or(qMember.realName.like("%" + screen.getAccount() + "%")));
        }
// if (!StringUtils.isEmpty(screen.getWalletAddress())) {
// criteria.add(qMemberAddress.address.eq(screen.getWalletAddress()));
// }

        if (!StringUtils.isEmpty(screen.getUnit())) {
            criteria.add(qMemberWallet.coin.unit.eq(screen.getUnit()));
        }

        if (screen.getMaxAllBalance() != null) {
            criteria.add(qMemberWallet.balance.add(qMemberWallet.frozenBalance).loe(screen.getMaxAllBalance()));
        }

        if (screen.getMinAllBalance() != null) {
            criteria.add(qMemberWallet.balance.add(qMemberWallet.frozenBalance).goe(screen.getMinAllBalance()));
        }

        if (screen.getMaxBalance() != null) {
            criteria.add(qMemberWallet.balance.loe(screen.getMaxBalance()));
        }

        if (screen.getMinBalance() != null) {
            criteria.add(qMemberWallet.balance.goe(screen.getMinBalance()));
        }

        if (screen.getMaxFrozenBalance() != null) {
            criteria.add(qMemberWallet.frozenBalance.loe(screen.getMaxFrozenBalance()));
        }

        if (screen.getMinFrozenBalance() != null) {
            criteria.add(qMemberWallet.frozenBalance.goe(screen.getMinFrozenBalance()));
        }

        Page<MemberWalletDTO> page = memberWalletService.joinFind(criteria, qMember, qMemberWallet, pageModel);
        return success(messageSource.getMessage("SUCCESS"), page);
    }

    @Override
    public MessageResult recharge(Admin admin, String unit, Long uid, BigDecimal amount) {
        Coin coin = coinService.findByUnit(unit);
        if (coin == null) {
            return error("The currency does not exist");
        }
        MemberWallet memberWallet = memberWalletService.findByCoinAndMemberId(coin, uid);
        Assert.notNull(memberWallet, "wallet null");
        memberWallet.setBalance(memberWallet.getBalance().add(amount));

        MemberTransaction memberTransaction = new MemberTransaction();
        memberTransaction.setFee(BigDecimal.ZERO);
        memberTransaction.setAmount(amount);
        memberTransaction.setMemberId(memberWallet.getMemberId());
        memberTransaction.setSymbol(unit);
        memberTransaction.setType(TransactionType.ADMIN_RECHARGE);
        memberTransaction.setCreateTime(DateUtil.getCurrentDate());
        memberTransaction.setRealFee("0");
        memberTransaction.setDiscountFee("0");
        memberTransaction = memberTransactionService.save(memberTransaction);

        String[] adminList = admins.split(",");
        for (int i = 0; i < adminList.length; i++) {
            sendEmailMsg(adminList[i], "Administrator manual recharge (user ID:" + uid + ", Currency:" + unit + ", quantity:" + amount + "); Operator:" + admin.getUsername() + "/" + admin.getMobilePhone(), "Manual recharge notification");
        }

        return success(messageSource.getMessage("SUCCESS"));
    }

    /**
     * Send an email
     *
     * @param email   the email
     * @param msg     the msg
     * @param subject the subject
     * @throws MessagingException
     * @throws IOException
     * @throws TemplateException
     */
    @Async
    public void sendEmailMsg(String email, String msg, String subject) {
        try {
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = null;
            helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(from);
            helper.setTo(email);
            helper.setSubject(company + "-" + subject);
            Map<String, Object> model = new HashMap<>(16);
            model.put("msg", msg);
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
            cfg.setClassForTemplateLoading(this.getClass(), "/templates");
            Template template = cfg.getTemplate("simpleMessage.ftl");
            String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
            helper.setText(html, true);

            // Send an email
            javaMailSender.send(mimeMessage);
            log.info("send email for {},content:{}", email, html);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public MessageResult resetAddress(String unit, long uid) {
        Member member = memberService.findOne(uid);
        Assert.notNull(member, "member null");
        try {
            ObjectNode json = objectMapper.createObjectNode();
            json.put("uid", member.getId());
            log.info("kafkaTemplate send : topic = {reset-member-address} , unit = {} , uid = {}", unit, json);
            kafkaTemplate.send("reset-member-address", unit, objectMapper.writeValueAsString(json));
            return MessageResult.success(messageSource.getMessage("SUCCESS"));
        } catch (Exception e) {
            log.error("Error resetting address", e);
            return MessageResult.error(messageSource.getMessage("REQUEST_FAILED"));
        }
    }

    @Override
    public MessageResult lockWallet(Long uid, String unit) {
        if (memberWalletService.lockWallet(uid, unit)) {
            return success(messageSource.getMessage("SUCCESS"));
        } else {
            return error(500, messageSource.getMessage("REQUEST_FAILED"));
        }
    }

    @Override
    public MessageResult unlockWallet(Long uid, String unit) {
        if (memberWalletService.unlockWallet(uid, unit)) {
            return success(messageSource.getMessage("SUCCESS"));
        } else {
            return error(500, messageSource.getMessage("REQUEST_FAILED"));
        }
    }
}
