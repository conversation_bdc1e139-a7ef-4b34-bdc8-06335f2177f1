package com.icetea.lotus.service.impl.ctc;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.CtcAcceptor;
import com.icetea.lotus.service.CtcAcceptorService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.ctc.AdminCtcAcceptorService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Admin ctc acceptor service.
 */
@Service
@RequiredArgsConstructor
public class AdminCtcAcceptorServiceImpl extends BaseController implements AdminCtcAcceptorService {

    private final CtcAcceptorService acceptorService;

    @Value("${spark.system.md5.key}")
    private String md5Key;

    private final LocaleMessageSourceService messageSource;

    @Override
    public MessageResult orderList(PageModel pageModel) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("status");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<CtcAcceptor> all = acceptorService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult payOrder(Long id, String password, Admin admin) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        CtcAcceptor acceptor = acceptorService.findOne(id);
        notNull(acceptor, "validate order.id!");
        if(acceptor.getStatus() == 1) {
            acceptor.setStatus(0);
        }else {
            acceptor.setStatus(1);
        }
        acceptorService.save(acceptor);
        return success();
    }

}
