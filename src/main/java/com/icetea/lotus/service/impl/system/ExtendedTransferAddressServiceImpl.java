package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.QTransferAddress;
import com.icetea.lotus.entity.spot.TransferAddress;
import com.icetea.lotus.model.screen.TransferAddressScreen;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.TransferAddressService;
import com.icetea.lotus.service.system.ExtendedTransferAddressService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Extended transfer address service.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExtendedTransferAddressServiceImpl implements ExtendedTransferAddressService {

    private final CoinService coinService;

    private final TransferAddressService transferAddressService;

    @Override
    public MessageResult merge(TransferAddress transferAddress, String coinName) {
        Coin coin = coinService.findOne(coinName);
        transferAddress.setCoin(coin);
        transferAddressService.save(transferAddress);
        return MessageResult.success("Save successfully");
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, TransferAddressScreen transferAddressScreen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(transferAddressScreen.getAddress())) {
            booleanExpressions.add(QTransferAddress.transferAddress.address.eq(transferAddressScreen.getAddress()));
        }
        if (StringUtils.isNotBlank(transferAddressScreen.getUnit())) {
            booleanExpressions.add(QTransferAddress.transferAddress.coin.unit.equalsIgnoreCase(transferAddressScreen.getAddress()));
        }
        if (transferAddressScreen.getStart() != null) {
            booleanExpressions.add(QTransferAddress.transferAddress.status.eq(transferAddressScreen.getStart()));
        }
        Page<TransferAddress> page = transferAddressService.findAll(PredicateUtils.getPredicate(booleanExpressions), pageModel);
        return MessageResult.getSuccessInstance("Get successful", page);
    }

    @Override
    public MessageResult detail(Long id) {
        TransferAddress transferAddress = transferAddressService.findById(id);
        return MessageResult.getSuccessInstance("Get successful", transferAddress);
    }

    @Override
    public MessageResult deletes(Long[] ids) {
        transferAddressService.deletes(ids);
        return MessageResult.success("Delete successfully");
    }
}
