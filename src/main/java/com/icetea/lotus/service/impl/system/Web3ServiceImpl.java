package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.system.Web3Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.Function;

import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;

import java.io.IOException;

@Service
@Slf4j
public class Web3ServiceImpl implements Web3Service {

    /**
     * Encodes and sends a call to a smart contract function using Web3j, returning the raw response value.
     * Logs an error if the contract call fails.
     *
     * @param web3j    the Web3j instance for blockchain interaction
     * @param contract the contract address
     * @param function the smart contract function to call
     * @return the raw value returned by the contract function
     * @throws Exception if the call fails
     */
    @Override
    public String callFunction(Web3j web3j, String contract, Function function) throws IOException {
        String encoded = FunctionEncoder.encode(function);
        EthCall response = web3j.ethCall(
                Transaction.createEthCallTransaction(null, contract, encoded),
                DefaultBlockParameterName.LATEST
        ).send();

        if (response.hasError()) {
            log.error("Error calling function: {}", response.getError().getMessage());
        }

        return response.getValue();
    }
}
