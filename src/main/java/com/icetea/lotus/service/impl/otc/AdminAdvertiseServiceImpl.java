package com.icetea.lotus.service.impl.otc;

import com.icetea.lotus.constant.AdvertiseControlStatus;
import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Advertise;
import com.icetea.lotus.entity.spot.QAdvertise;
import com.icetea.lotus.model.screen.AdvertiseScreen;
import com.icetea.lotus.service.AdvertiseService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.otc.AdminAdvertiseService;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * The type Admin advertise service.
 */
@Service
public class AdminAdvertiseServiceImpl extends BaseAdminController implements AdminAdvertiseService {

    private final AdvertiseService advertiseService;

    private final LocaleMessageSourceService messageSource;

    public AdminAdvertiseServiceImpl(BaseAdminService baseAdminService, AdvertiseService advertiseService, LocaleMessageSourceService messageSource) {
        super(baseAdminService);
        this.advertiseService = advertiseService;
        this.messageSource = messageSource;
    }

    @Override
    public MessageResult detail(Long id) {
        if (id == null) {
            return error("Issue must be passed");
        }
        Advertise one = advertiseService.findOne(id);
        if (one == null) {
            return error("Advertisements without this id");
        }
        return success(messageSource.getMessage("SUCCESS"), one);
    }

    @Override
    public MessageResult statue(Long[] ids, AdvertiseControlStatus status) {
        advertiseService.turnOffBatch(status, ids);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult outExcel(Date startTime, Date endTime, AdvertiseType advertiseType, String realName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<BooleanExpression> booleanExpressionList = getBooleanExpressionList(startTime, endTime, advertiseType, realName);
        List<Advertise> list = advertiseService.queryWhereOrPage(booleanExpressionList, null, null).getContent();
        return new FileUtil<Advertise>(messageSource).exportExcel(request, response, list, "order");
    }

    @Override
    public List<BooleanExpression> getBooleanExpressionList(Date startTime, Date endTime, AdvertiseType advertiseType, String realName) {
        QAdvertise qEntity = QAdvertise.advertise;
        List<BooleanExpression> booleanExpressionList = new ArrayList();
        booleanExpressionList.add(qEntity.status.in(AdvertiseControlStatus.PUT_ON_SHELVES, AdvertiseControlStatus.PUT_OFF_SHELVES));
        if (startTime != null) {
            booleanExpressionList.add(qEntity.createTime.gt(startTime));
        }
        if (endTime != null) {
            booleanExpressionList.add(qEntity.createTime.lt(endTime));
        }
        if (advertiseType != null) {
            booleanExpressionList.add(qEntity.advertiseType.eq(advertiseType));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            booleanExpressionList.add(qEntity.member.realName.like("%" + realName + "%"));
        }
        return booleanExpressionList;
    }

    @Override
    public MessageResult page(PageModel pageModel, AdvertiseScreen screen) {
        Predicate predicate = getPredicate(screen);
        Page<Advertise> all = advertiseService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    private Predicate getPredicate(AdvertiseScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (screen.getStatus() != AdvertiseControlStatus.TURNOFF && screen.getStatus() != null) {
            booleanExpressions.add(QAdvertise.advertise.status.eq(screen.getStatus()));
        }
        if (screen.getStatus() == null) {
            booleanExpressions.add(QAdvertise.advertise.status.eq(AdvertiseControlStatus.PUT_ON_SHELVES).or(QAdvertise.advertise.status.eq(AdvertiseControlStatus.PUT_OFF_SHELVES)));
        }
        if (screen.getAdvertiseType() != null) {
            booleanExpressions.add(QAdvertise.advertise.advertiseType.eq(screen.getAdvertiseType()));
        }
        if (StringUtils.isNotBlank(screen.getAccount())) {
            booleanExpressions.add(QAdvertise.advertise.member.realName.like("%" + screen.getAccount() + "%")
                    .or(QAdvertise.advertise.member.username.like("%" + screen.getAccount() + "%"))
                    .or(QAdvertise.advertise.member.mobilePhone.like((screen.getAccount() + "%")))
                    .or(QAdvertise.advertise.member.email.like((screen.getAccount() + "%"))));
        }
        if (screen.getPayModel() != null) {
            booleanExpressions.add(QAdvertise.advertise.payMode.contains(screen.getPayModel()));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }
}
