package com.icetea.lotus.service.impl.businessAuth;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.DepositStatusEnum;
import com.icetea.lotus.constant.MemberLevelEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.BusinessAuthApply;
import com.icetea.lotus.entity.spot.BusinessCancelApply;
import com.icetea.lotus.entity.spot.DepositRecord;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QBusinessCancelApply;
import com.icetea.lotus.service.BusinessAuthApplyService;
import com.icetea.lotus.service.BusinessCancelApplyService;
import com.icetea.lotus.service.DepositRecordService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.businessAuth.BusinessCancelAppliedService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.icetea.lotus.constant.CertifiedBusinessStatus.CANCEL_AUTH;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.NOT_CERTIFIED;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.RETURN_FAILED;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.RETURN_SUCCESS;
import static com.icetea.lotus.constant.CertifiedBusinessStatus.VERIFIED;

/**
 * The type Business cancel apply service.
 */
@Service
@RequiredArgsConstructor
public class BusinessCancelApplyServiceImpl extends BaseController implements BusinessCancelAppliedService {

    private static Logger logger = LoggerFactory.getLogger(BusinessCancelApplyServiceImpl.class);

    private final BusinessCancelApplyService businessCancelApplyService;
    private final DepositRecordService depositRecordService;
    private final BusinessAuthApplyService businessAuthApplyService;
    private final MemberWalletService memberWalletService;
    private final MemberService memberService;
    private final LocaleMessageSourceService msService;

    @Override
    public MessageResult pageQuery(PageModel pageModel, String account, CertifiedBusinessStatus status, Date startDate, Date endDate) {
        List<BooleanExpression> predicates = new ArrayList<>();
        if (!StringUtils.isEmpty(account)) {
            predicates.add(QBusinessCancelApply.businessCancelApply.member.username.like("%" + account + "%")
                    .or(QBusinessCancelApply.businessCancelApply.member.mobilePhone.like("%" + account + "%"))
                    .or(QBusinessCancelApply.businessCancelApply.member.email.like("%" + account + "%"))
                    .or(QBusinessCancelApply.businessCancelApply.member.realName.like("%" + account + "%")));
        }
        predicates.add(QBusinessCancelApply.businessCancelApply.status.in(CANCEL_AUTH, RETURN_FAILED, RETURN_SUCCESS));
        if (status != null) {
            predicates.add(QBusinessCancelApply.businessCancelApply.status.eq(status));
        }
        if (startDate != null) {
            predicates.add(QBusinessCancelApply.businessCancelApply.cancelApplyTime.goe(startDate));
        }
        if (endDate != null) {
            predicates.add(QBusinessCancelApply.businessCancelApply.cancelApplyTime.loe(endDate));
        }

        Page<BusinessCancelApply> page = businessCancelApplyService.findAll(PredicateUtils.getPredicate(predicates), pageModel);

        for (BusinessCancelApply businessCancelApply : page.getContent()) {
            DepositRecord depositRecord = depositRecordService.findOne(businessCancelApply.getDepositRecordId());
            businessCancelApply.setDepositRecord(depositRecord);
        }
        return success(page);
    }

    @Override
    public MessageResult pass(Long id, BooleanEnum success, String reason) {
        Optional<BusinessCancelApply> businessCancelApply = businessCancelApplyService.findByBusinessCancelApplyId(id);
        Member member = businessCancelApply.map(BusinessCancelApply::getMember).orElse(null);
        List<BusinessAuthApply> businessAuthApplyList = businessAuthApplyService.findByMemberAndCertifiedBusinessStatus(member, VERIFIED);
        if (businessAuthApplyList == null || businessAuthApplyList.isEmpty()) {
            return error("data exception,businessAuthApply not exist....");
        }
        BusinessAuthApply businessAuthApply = businessAuthApplyList.get(0);
        /**
         * Process Cancel Application Log
         */
        businessCancelApply.ifPresent(businessCancelApply1 ->
                {
                    businessCancelApply1.setHandleTime(DateUtil.getCurrentDate());
                    businessCancelApply1.setDepositRecordId(businessAuthApply.getDepositRecordId());
                    businessCancelApply1.setDetail(reason);
                }
        );
        if (success == BooleanEnum.IS_TRUE) {
            businessCancelApply.ifPresent(businessCancelApply2 ->
                    {
                        businessCancelApply2.setStatus(RETURN_SUCCESS);
                        businessCancelApplyService.save(businessCancelApply2);
                    }
            );


            // Cancel merchant certification and pass
            // member.setCertifiedBusinessStatus(RETURN_SUCCESS);//Not certified
            member.setCertifiedBusinessStatus(NOT_CERTIFIED);
            member.setMemberLevel(MemberLevelEnum.REALNAME);
            memberService.save(member);

            List<DepositRecord> depositRecordList = depositRecordService.findByMemberAndStatus(member, DepositStatusEnum.PAY);
            if (depositRecordList != null && depositRecordList.size() > 0) {
                BigDecimal deposit = BigDecimal.ZERO;

                /**
                 * Change margin record
                 */
                for (DepositRecord depositRecord : depositRecordList) {
//                  TODO  depositRecord.setStatus(DepositStatusEnum.GET_BACK);
                    deposit = deposit.add(depositRecord.getAmount());
                    depositRecordService.save(depositRecord);
                }

                /**
                 * Return of margin
                 */
                if (businessAuthApplyList != null && businessAuthApplyList.size() > 0) {
                    MemberWallet memberWallet = memberWalletService.findByCoinUnitAndMemberId(businessAuthApply.getBusinessAuthDeposit().getCoin().getUnit(), member.getId());
                    memberWallet.setBalance(memberWallet.getBalance().add(deposit));
                    // memberWallet.setFrozenBalance(memberWallet.getFrozenBalance().subtract(deposit));
                    memberWalletService.save(memberWallet);
                }
            }
            /**
             * Change the certification application status
             */
            return MessageResult.success(msService.getMessage("PASS_THE_AUDIT"), reason);
        } else {
            // The review fails, the merchant maintains the certified status
            // member.setCertifiedBusinessStatus(RETURN_FAILED);
            member.setCertifiedBusinessStatus(VERIFIED);
            member.setMemberLevel(MemberLevelEnum.IDENTIFICATION);
            memberService.save(member);
            businessCancelApply.ifPresent(businessCancelApply3 ->
                    {
                        businessCancelApply3.setStatus(RETURN_FAILED);
                        businessCancelApplyService.save(businessCancelApply3);
                    }
            );
            return MessageResult.success(msService.getMessage("AUDIT_DOES_NOT_PASS"), reason);
        }
    }

    @Override
    public MessageResult detail(Long id) {
        Optional<BusinessCancelApply> businessCancelApply = businessCancelApplyService.findByBusinessCancelApplyId(id);
        DepositRecord depositRecord = depositRecordService.findOne(businessCancelApply.map(BusinessCancelApply::getDepositRecordId).orElse(null));
        Member member = businessCancelApply.map(BusinessCancelApply::getMember).orElse(null);
        Map<String, Object> map1 = businessCancelApplyService.getBusinessOrderStatistics(member.getId());
        logger.info("Member order information: {}", map1);
        Map<String, Object> map2 = businessCancelApplyService.getBusinessAppealStatistics(member.getId());
        logger.info("Member appeal information: {}", map2);
        Long advertiseNum = businessCancelApplyService.getAdvertiserNum(member.getId());
        logger.info("Member advertising information: {}", advertiseNum);
        Map<String, Object> map = new HashMap<>();
        map.putAll(map1);
        map.putAll(map2);
        map.put("advertiseNum", advertiseNum);
        map.put("businessCancelApply", businessCancelApply);
        map.put("depositRecord", depositRecord);
        logger.info("Information related to membership cancellation: {}", map);
        return success(map);
    }

    @Override
    public MessageResult getSearchStatus() {
        CertifiedBusinessStatus[] statuses = CertifiedBusinessStatus.values();
        List<Map> list = new ArrayList<>();
        for (CertifiedBusinessStatus status : statuses) {
            if (status.getOrdinal() < CertifiedBusinessStatus.CANCEL_AUTH.getOrdinal()) {
                continue;
            }
            Map map = new HashMap();
            map.put("name", status.getName());
            map.put("value", status.getOrdinal());
            list.add(map);
        }
        return success(list);
    }
}
