package com.icetea.lotus.service.impl.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionTypeEnum;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.controller.finance.FinanceStatisticsController;
import com.icetea.lotus.model.screen.ExchangeTradeScreen;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.finance.FinanceStatisticsService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.TurnoverStatisticsVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.bson.types.Decimal128;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * The type Finance statistics service.
 */
@RequiredArgsConstructor
@Service
public class FinanceStatisticsServiceImpl extends BaseController implements FinanceStatisticsService {

    private final MongoTemplate mongoTemplate;

    private final ExchangeOrderService exchangeOrderService;

    private static final Logger logger = LoggerFactory.getLogger(FinanceStatisticsController.class);

    private final LocaleMessageSourceService messageSource;

    @Override
    public MessageResult page(PageModel pageModel, ExchangeTradeScreen screen) {
         /* ExchangeOrder
        StringBuilder headSql = new StringBuilder("select a.symbol symbol,a.price price,a.tradeAmount amount,a.turnover money , ")
                .append("FROM_UNIXTIME(a.completedTime/1000, '%Y-%m-%d %H:%i:%S') as completedTime,a.orderId buyOrderId,")
                .append("b.username buyUsername ")
                .append("b.level promotionLevel ") ;
        StringBuilder endSql = new StringBuilder(" from member a join member_promotion b on a.inviter_id = b.inviter_id and a.inviter_id = "+memberId);
        StringBuilder countHead = new StringBuilder("select count(*) ") ;*/
        return null;
    }

    @Override
    public MessageResult getResult(String[] types, Date startDate, Date endDate, String unit) {
        Assert.notNull(types, "type must not be null ");
        if (endDate == null) {
            endDate = DateUtil.getDate(new Date(), 1);
        }

        ProjectionOperation projectionOperation = Aggregation.project("date", "type", "unit", "amount").andExclude("_id");

        List<Criteria> criterias = new ArrayList<>();

        if (startDate != null) {
            criterias.add(Criteria.where("date").gte(startDate));
        }

        criterias.add(Criteria.where("date").lte(endDate));

        if (!StringUtils.isEmpty(unit)) {
            criterias.add(Criteria.where("unit").is(unit));
        }

        MatchOperation matchOperation = Aggregation.match(
                Criteria.where("type").in(types)
                        .andOperator(criterias.toArray(new Criteria[criterias.size()]))

        );

        GroupOperation groupOperation = Aggregation.group("type", "unit").sum("amount").as("amount");

        Aggregation aggregation = Aggregation.newAggregation(projectionOperation, matchOperation, groupOperation);

        AggregationResults<Map> aggregationResults = this.mongoTemplate.aggregate(aggregation, "turnover_statistics", Map.class);

        List<Map> list = aggregationResults.getMappedResults();

        Set<String> units = new HashSet<>();

        for (Map map : list) {
            /* map.put("date",date);*/
            map.put("amount", ((Decimal128) map.get("amount")).bigDecimalValue());

            units.add(map.get("unit").toString());

            logger.info("Transaction information: {}", map);
        }

        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), list);
    }

    @Override
    public MessageResult getFee(TransactionTypeEnum type, Date startDate, Date endDate, String unit) {
        Assert.notNull(type, "type must not be null ");

        Assert.isTrue(type == TransactionTypeEnum.OTC_NUM ||
                type == TransactionTypeEnum.EXCHANGE ||
                type == TransactionTypeEnum.WITHDRAW, "This interface is for fiat currency statistics, and the type can only be 0 (fiat currency) or 2 (coin currency) or 6 (withdrawal) handling fees");

        if (endDate == null) {
            endDate = DateUtil.getDate(new Date(), 1);
        }

        Assert.notNull(startDate, "startDate must not be null ");

        ProjectionOperation projectionOperation = Aggregation.project("date", "type", "unit", "fee").andExclude("_id");

        List<Criteria> criterias = new ArrayList<>();

        criterias.add(Criteria.where("date").gte(startDate));

        criterias.add(Criteria.where("date").lte(endDate));

        if (!StringUtils.isEmpty(unit)) {
            criterias.add(Criteria.where("unit").is(unit));
        }

        MatchOperation matchOperation = Aggregation.match(
                Criteria.where("type").is(type.toString())
                        .andOperator(criterias.toArray(new Criteria[criterias.size()]))

        );

        GroupOperation groupOperation = Aggregation.group("type", "unit").sum("fee").as("fee");

        Aggregation aggregation = Aggregation.newAggregation(projectionOperation, matchOperation, groupOperation);

        AggregationResults<Map> aggregationResults = this.mongoTemplate.aggregate(aggregation, "turnover_statistics", Map.class);

        List<Map> list = aggregationResults.getMappedResults();

        for (Map map : list) {
            map.put("fee", ((Decimal128) map.get("fee")).bigDecimalValue());
            logger.info("Processing fee information:{}", map);
        }

        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), list);
    }

    @Override
    public MessageResult recharge(TransactionTypeEnum type, Date startDate, Date endDate) {
        Assert.isTrue(type == TransactionTypeEnum.WITHDRAW || type == TransactionTypeEnum.RECHARGE, "Type can only be 5 (coin recharge) or 6 (withdrawal)");

        if (endDate == null) {
            endDate = DateUtil.getDate(new Date(), 1);
        }

        Assert.notNull(startDate, "startDate must not be null");

        ProjectionOperation projectionOperation = Aggregation.project("date", "year", "month", "day", "type", "unit", "amount", "fee").andExclude("_id");

        MatchOperation matchOperation = Aggregation.match(
                Criteria.where("type").is(type.toString())
                        .andOperator(Criteria.where("date").gte(startDate), Criteria.where("date").lte(endDate))
        );

        GroupOperation groupOperation = Aggregation.group("type", "unit").sum("amount").as("amount").sum("fee").as("fee");

        Aggregation aggregation = Aggregation.newAggregation(projectionOperation, matchOperation, groupOperation);

        AggregationResults<TurnoverStatisticsVO> aggregationResults = this.mongoTemplate.aggregate(aggregation, "turnover_statistics", TurnoverStatisticsVO.class);

        List<TurnoverStatisticsVO> list = aggregationResults.getMappedResults();

        logger.info("{}lump sum:{}", type == TransactionTypeEnum.WITHDRAW ? "Withdrawal of coins" : "Recharge coins", list);

        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), list);
    }
}
