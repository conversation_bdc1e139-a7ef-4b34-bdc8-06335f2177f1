package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberLevel;
import com.icetea.lotus.service.MemberLevelService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedMemberLevelService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * The type Extended member level service.
 */
@Service
public class ExtendedMemberLevelServiceImpl extends BaseAdminController implements ExtendedMemberLevelService {

    private final MemberLevelService memberLevelService;

    public ExtendedMemberLevelServiceImpl(BaseAdminService baseAdminService, MemberLevelService memberLevelService) {
        super(baseAdminService);
        this.memberLevelService = memberLevelService;
    }

    @Override
    public MessageResult findAll() {
        List<MemberLevel> memberLevels = memberLevelService.findAll();
        MessageResult messageResult = success();
        messageResult.setData(memberLevels);
        return messageResult;
    }

    @Override
    public MessageResult update(MemberLevel memberLevel) {
        if (memberLevel.getId() == null) {
            return error("The primary key must not be empty");
        }
        MemberLevel one = memberLevelService.findOne(memberLevel.getId());
        if (one == null) {
            return error("The modified object does not exist");
        }
        if (memberLevel.getIsDefault() && !one.getIsDefault())
        // Modify the object to default. Originally it was false. If the default level isDefault is false
        {
            memberLevelService.updateDefault();
        }
        MemberLevel save = memberLevelService.save(memberLevel);
        return success(save);
    }
}
