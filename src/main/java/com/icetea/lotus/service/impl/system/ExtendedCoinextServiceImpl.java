package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinprotocolDTO;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.entity.spot.QCoinext;
import com.icetea.lotus.model.screen.CoinextScreen;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinextService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedCoinextService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Extended coinext service.
 */
@Slf4j
@Service
public class ExtendedCoinextServiceImpl extends BaseAdminController implements ExtendedCoinextService {

    private final CoinService coinService;

    private final CoinprotocolService coinprotocolService;

    private final CoinextService coinextService;

    private final RedisTemplate<String, String> redisTemplate;

    public ExtendedCoinextServiceImpl(BaseAdminService baseAdminService, CoinService coinService, CoinprotocolService coinprotocolService, CoinextService coinextService, RedisTemplate<String, String> redisTemplate) {
        super(baseAdminService);
        this.coinService = coinService;
        this.coinprotocolService = coinprotocolService;
        this.coinextService = coinextService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public MessageResult coinList() {
        List<CoinDTO> list = coinService.list();

        return success(list);
    }

    @Override
    public MessageResult protocolList() {
        List<CoinprotocolDTO> list = coinprotocolService.list();

        return success(list);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, CoinextScreen coinextScreen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        if (!StringUtils.isBlank(coinextScreen.getCoinname())) {
            booleanExpressions.add(QCoinext.coinext.coinname.eq(coinextScreen.getCoinname()));
        }

        if (!StringUtils.isBlank(coinextScreen.getExt())) {
            booleanExpressions.add(QCoinext.coinext.ext.eq(coinextScreen.getExt()));
        }

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<Coinext> all = coinextService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult merge(Coinext coinext) {
        MessageResult result;

        // Check if the query exists
        Coinext one = coinextService.findFirstByCoinnameAndProtocol(coinext.getCoinname(), coinext.getProtocol());
        if (coinext.getId() != null) {
            if (one != null && !one.getId().equals(coinext.getId())) {
                result = error("The currency of the current protocol already exists");
                return result;
            }
        } else if (one != null) {
            result = error("The currency of the current protocol already exists");
            return result;
        }

        Coinprotocol byProtocol = coinprotocolService.findByProtocol(coinext.getProtocol());

        if (byProtocol == null) {
            result = error("The current protocol does not exist");
            return result;
        }

        coinext.setProtocolname(byProtocol.getProtocolname());

        // Delete the redis cache
        redisTemplate.delete("coinext");


        coinext = coinextService.save(coinext);
        result = success("Operation is successful");
        result.setData(coinext);
        return result;
    }
}
