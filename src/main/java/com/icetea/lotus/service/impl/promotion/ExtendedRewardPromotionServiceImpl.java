package com.icetea.lotus.service.impl.promotion;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.PromotionRewardType;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.QRewardPromotionSetting;
import com.icetea.lotus.entity.spot.RewardPromotionSetting;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.RewardPromotionSettingService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.promotion.ExtendedRewardPromotionService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * The type Extended reward promotion service.
 */
@Service
public class ExtendedRewardPromotionServiceImpl extends BaseAdminController implements ExtendedRewardPromotionService {

    private final RewardPromotionSettingService rewardPromotionSettingService;

    private final CoinService coinService;
    private final LocaleMessageSourceService messageSource;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${spark.system.md5.key}")
    private String md5Key;

    public ExtendedRewardPromotionServiceImpl(BaseAdminService baseAdminService, RewardPromotionSettingService rewardPromotionSettingService, CoinService coinService, LocaleMessageSourceService messageSource) {
        super(baseAdminService);
        this.rewardPromotionSettingService = rewardPromotionSettingService;
        this.coinService = coinService;
        this.messageSource = messageSource;
    }

    @Override
    public MessageResult merge(RewardPromotionSetting setting, Admin admin, String unit, String password) {
        password = Encrypt.MD5(password + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));
        Coin coin = null;
        if (setting.getType() != PromotionRewardType.EXCHANGE_TRANSACTION) {
            coin = coinService.findByUnit(unit);
            setting.setCoin(coin);
        }
        RewardPromotionSetting rewardPromotionSetting = rewardPromotionSettingService.findByType(setting.getType());
        if (setting.getId() == null && rewardPromotionSetting != null) {
            return error("This type of configuration already exists");
        }
        setting.setInfo("{\"one\":" + setting.getOne() + ",\"two\":" + setting.getTwo() + "}");
        rewardPromotionSettingService.save(setting);
        return MessageResult.success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult detail(Long id) {
        RewardPromotionSetting setting = rewardPromotionSettingService.findById(id);
        if (setting == null) {
            return error("This configuration does not exist");
        }
        String jsonStr = setting.getInfo();
        if (!StringUtils.isEmpty(jsonStr)) {

            try {
                JsonNode jsonNode = objectMapper.readTree(setting.getInfo());
                setting.setOne(new BigDecimal(jsonNode.get("one").asText()));
                setting.setTwo(new BigDecimal(jsonNode.get("two").asText()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

        }
        return success(messageSource.getMessage("SUCCESS"), setting);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, BooleanEnum enable, PromotionRewardType type) {
        BooleanExpression predicate = null;
        if (type != null) {
            predicate.andAnyOf(QRewardPromotionSetting.rewardPromotionSetting.type.eq(type));
        }
        Page<RewardPromotionSetting> all = rewardPromotionSettingService.findAll(predicate, pageModel);
        for (RewardPromotionSetting setting : all) {
            if (StringUtils.isEmpty(setting.getInfo())) {
                continue;
            }
            try {
                JsonNode jsonNode = objectMapper.readTree(setting.getInfo());
                setting.setOne(new BigDecimal(jsonNode.get("one").asText()));
                setting.setTwo(new BigDecimal(jsonNode.get("two").asText()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return success(all);
    }

    @Override
    public MessageResult deletes(long[] ids) {
        Assert.notNull(ids, "ids cannot be null");
        rewardPromotionSettingService.deletes(ids);
        return MessageResult.success(messageSource.getMessage("SUCCESS"));
    }
}
