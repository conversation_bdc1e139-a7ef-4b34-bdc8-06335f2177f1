package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.OrderStatus;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Statistics;
import com.icetea.lotus.service.OrderDetailAggregationService;
import com.icetea.lotus.service.StatisticsService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedStatisticsService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Extended statistics service.
 */
@Slf4j
@Service
public class ExtendedStatisticsServiceImpl extends BaseAdminController implements ExtendedStatisticsService {

    private final StatisticsService statisticsService;

    private final OrderDetailAggregationService orderDetailAggregationService ;

    public ExtendedStatisticsServiceImpl(BaseAdminService baseAdminService, StatisticsService statisticsService, OrderDetailAggregationService orderDetailAggregationService) {
        super(baseAdminService);
        this.statisticsService = statisticsService;
        this.orderDetailAggregationService = orderDetailAggregationService;
    }

    @Override
    public MessageResult memberStatistics(String startTime, String endTime) {

        if (startTime == null || endTime == null) {
            return error("The parameter cannot be null");
        }
        String sql = "SELECT COUNT(t.id) AS i,t.date FROM( SELECT m.id,DATE_FORMAT( m.registration_time, \"%Y-%m-%e\" ) AS date FROM member m WHERE m.registration_time BETWEEN:startTime AND:endTime ) AS t GROUP BY t.date order BY unix_timestamp(t.date)";
        List statistics = statisticsService.getStatistics(startTime, endTime, sql);
        if (statistics != null && statistics.size() > 0) {
            return success(statistics);
        }
        return error("The requested data does not exist");
    }

    @Override
    public MessageResult delegationStatistics(String startTime, String endTime) {
        if (startTime == null || endTime == null) {
            return error("The parameter cannot be null");
        }
        String sql = "SELECT t.date, COUNT(t.id) AS i FROM ( SELECT a.id, DATE_FORMAT(a.create_time, \"%Y-%m-%e\") date FROM advertise a WHERE a.`level`= 2 and a.create_time BETWEEN:startTime AND:endTime ) t GROUP BY t.date order BY unix_timestamp(t.date)";
        List<Statistics> list = statisticsService.getStatistics(startTime, endTime, sql);
        if (list != null && list.size() > 0) {
            return success(list);
        }
        return error("The requested data does not exist");
    }

    @Override
    public MessageResult orderStatistics(String startTime, String endTime) {
        if (startTime == null || endTime == null) {
            return error("The parameter cannot be null");
        }
        String sql = "SELECT t.date, COUNT(t.id) AS i FROM ( SELECT o.id, DATE_FORMAT(o.pay_time, \"%Y-%m-%e\") date FROM otc_order o WHERE o.`status` = 3 AND o.pay_time BETWEEN:startTime AND:endTime ) t GROUP BY t.date order BY unix_timestamp(t.date)";
        List statistics = statisticsService.getStatistics(startTime, endTime, sql);
        if (statistics != null && statistics.size() > 0) {
            return success(statistics);
        }
        return error("The requested data does not exist");
    }

    @Override
    public MessageResult dashboard() {
        Map<String, Integer> map = new HashMap<String, Integer>();
        // New users today
        int regMember = statisticsService.getLatelyRegMember(0);
        map.put("regMember", regMember);
        // New orders added today
        String today = DateUtil.getDate();
        int orderNum = statisticsService.getLatelyOrder(today, today, -1);
        map.put("orderNum", orderNum);
        // Order Status Status Status
        int unpaidOrderNum = statisticsService.getLatelyOrder(OrderStatus.NONPAYMENT);// Not paid
        map.put("unpaidOrderNum", unpaidOrderNum);
        int unconfirmedOrderNum = statisticsService.getLatelyOrder(OrderStatus.PAID);// Payed Care Confirm Order
        map.put("unconfirmedOrderNum", unconfirmedOrderNum);
        int appealOrderNum = statisticsService.getLatelyOrder(OrderStatus.APPEAL);// Number of orders in the complaint
        map.put("appealOrderNum", appealOrderNum);
        int completedOrderNum = statisticsService.getLatelyOrder(OrderStatus.COMPLETED);// Number of completed orders
        map.put("completedOrderNum", completedOrderNum);
        // Number of ads that are about to expire
        int latelyAdvertise = statisticsService.getLatelyAdvertise(1);
        // Number of real-name reviews required
        return success(map);
    }
}
