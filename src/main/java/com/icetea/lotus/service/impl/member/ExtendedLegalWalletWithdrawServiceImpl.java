package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.WithdrawStatus;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.LegalWalletWithdraw;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QLegalWalletWithdraw;
import com.icetea.lotus.model.screen.LegalWalletWithdrawScreen;
import com.icetea.lotus.service.LegalWalletWithdrawService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedLegalWalletWithdrawService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;

/**
 * The type Extended legal wallet withdraw service.
 */
@Service
public class ExtendedLegalWalletWithdrawServiceImpl extends BaseAdminController implements ExtendedLegalWalletWithdrawService {

    private final LegalWalletWithdrawService legalWalletWithdrawService;

    private final MemberWalletService walletService;

    public ExtendedLegalWalletWithdrawServiceImpl(BaseAdminService baseAdminService, LegalWalletWithdrawService legalWalletWithdrawService, MemberWalletService walletService) {
        super(baseAdminService);
        this.legalWalletWithdrawService = legalWalletWithdrawService;
        this.walletService = walletService;
    }

    @Override
    public MessageResult page(PageModel pageModel, LegalWalletWithdrawScreen screen) {
        Predicate predicate = getPredicate(screen);
        Page<LegalWalletWithdraw> page = legalWalletWithdrawService.findAll(predicate, pageModel);
        return success(page);
    }

    @Override
    public MessageResult detail(Long id) {
        LegalWalletWithdraw one = legalWalletWithdrawService.findOne(id);
        Assert.notNull(one, "validate id!");
        return success(one);
    }

    @Override
    public MessageResult pass(Long id) {
        // Verify data
        LegalWalletWithdraw one = legalWalletWithdrawService.findOne(id);
        Assert.notNull(one, "validate id!");
        Assert.isTrue(one.getStatus() == WithdrawStatus.PROCESSING, "Review has ended!");
        // Review passed
        legalWalletWithdrawService.pass(one);
        return success();
    }

    @Override
    public MessageResult noPass(Long id) {
        // Verify withdraw cash
        LegalWalletWithdraw one = legalWalletWithdrawService.findOne(id);
        Assert.notNull(one, "validate id!");
        Assert.isTrue(one.getStatus() == WithdrawStatus.PROCESSING, "Review has ended!");
        // Verify wallet
        MemberWallet wallet = walletService.findByCoinAndMember(one.getCoin(), one.getMember());
        Assert.notNull(wallet, "wallet null!");
        // Don't modify the wallet cash withdrawal status
        legalWalletWithdrawService.noPass(wallet, one);
        return success(one);
    }

    @Override
    public MessageResult remit(Long id, String paymentInstrument) {
        // Verify cash withdrawal order
        LegalWalletWithdraw one = legalWalletWithdrawService.findOne(id);
        Assert.notNull(one, "validate id!");
        Assert.isTrue(one.getStatus() == WithdrawStatus.WAITING, "The payment has ended!");
        // Verify wallet
        MemberWallet wallet = walletService.findByCoinAndMember(one.getCoin(), one.getMember());
        Assert.notNull(wallet, "wallet null!");
        // Payment operation
        legalWalletWithdrawService.remit(paymentInstrument, one, wallet);
        return success(one);
    }

    // condition
    private Predicate getPredicate(LegalWalletWithdrawScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(screen.getUsername())) {
            booleanExpressions.add(QLegalWalletWithdraw.legalWalletWithdraw.member.username.eq(screen.getUsername()));
        }
        if (screen.getStatus() != null) {
            booleanExpressions.add(QLegalWalletWithdraw.legalWalletWithdraw.status.eq(screen.getStatus()));
        }
        if (StringUtils.isNotBlank(screen.getCoinName())) {
            booleanExpressions.add(QLegalWalletWithdraw.legalWalletWithdraw.coin.name.eq(screen.getCoinName()));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }
}
