package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberApplicationConfig;
import com.icetea.lotus.service.MemberApplicationConfigService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedMemberApplicationConfigService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * The type Extended member application config service.
 */
@Slf4j
@Service
public class ExtendedMemberApplicationConfigServiceImpl extends BaseAdminController implements ExtendedMemberApplicationConfigService {

    private final MemberApplicationConfigService memberApplicationConfigService ;

    public ExtendedMemberApplicationConfigServiceImpl(BaseAdminService baseAdminService, MemberApplicationConfigService memberApplicationConfigService) {
        super(baseAdminService);
        this.memberApplicationConfigService = memberApplicationConfigService;
    }

    @Override
    public MessageResult merge(MemberApplicationConfig memberApplicationConfig) {
        memberApplicationConfigService.save(memberApplicationConfig);
        return MessageResult.getSuccessInstance("Save successfully",memberApplicationConfig);
    }

    @Override
    public MessageResult query() {
        return MessageResult.getSuccessInstance("Get successful",memberApplicationConfigService.get());
    }
}
