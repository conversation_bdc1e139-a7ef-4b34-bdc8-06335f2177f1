package com.icetea.lotus.service.impl;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.controller.code.SmsProviderController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.code.SmsProviderService;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.concurrent.TimeUnit;


/**
 * The type Sms provider service.
 */
@Service
@RequiredArgsConstructor
public class SmsProviderServiceImpl extends BaseController implements SmsProviderService {

    private Logger logger = LoggerFactory.getLogger(SmsProviderController.class);

    private final SMSProvider smsProvider;

    private final RedisTemplate redisTemplate;


    private final LocaleMessageSourceService msService;

    @Override
    public MessageResult sendReviseCode(Admin admin) {
        Assert.notNull(admin, msService.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));
        return sendCode(admin.getMobilePhone(), SysConstant.ADMIN_COIN_REVISE_PHONE_PREFIX);
    }

    @Override
    public MessageResult sendExchangeCoinSet(Admin admin) {
        Assert.notNull(admin, msService.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));
        return sendCode(admin.getMobilePhone(), SysConstant.ADMIN_EXCHANGE_COIN_SET_PREFIX);
    }

    @Override
    public MessageResult sendTransfer(Admin admin) {
        Assert.notNull(admin, msService.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));
        return sendCode(admin.getMobilePhone(), SysConstant.ADMIN_COIN_TRANSFER_COLD_PREFIX);
    }

    @Override
    public MessageResult send(String phone) {
        return sendCode(phone, SysConstant.ADMIN_LOGIN_PHONE_PREFIX);
    }

    private MessageResult sendCode(String phone, String prefix) {
        Assert.notNull(phone, msService.getMessage("NO_CELL_PHONE_NUMBER"));
        MessageResult result;
        String randomCode = String.valueOf(GeneratorUtil.getRandomNumber(100000, 999999));
        try {
            ValueOperations valueOperations = redisTemplate.opsForValue();
            String key = prefix + phone;
            long expire = valueOperations.getOperations().getExpire(key, TimeUnit.SECONDS);
            if (expire < 600 && expire > 540) {
                return error(msService.getMessage("SEND_CODE_FAILURE_ONE"));
            }
            result = smsProvider.sendVerifyMessage(phone, randomCode);
            if (result.getCode() == 0) {
                logger.info("SMS verification code: {}", randomCode);
                valueOperations.set(key, randomCode, 10, TimeUnit.MINUTES);
                return success(msService.getMessage("SEND_CODE_SUCCESS") + phone);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return error(msService.getMessage("REQUEST_FAILED"));
    }
}
