package com.icetea.lotus.service.impl.activity;


import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.entity.spot.Activity;
import com.icetea.lotus.entity.spot.ActivityOrder;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.LockedOrder;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.MiningOrder;
import com.icetea.lotus.model.ActivityRequest;
import com.icetea.lotus.service.ActivityOrderService;
import com.icetea.lotus.service.ActivityService;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.LockedOrderService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.MiningOrderService;
import com.icetea.lotus.service.activity.ActivitiesService;
import com.icetea.lotus.service.activity.ActivityMapper;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.icetea.lotus.vendor.provider.SMSProvider;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.icetea.lotus.entity.spot.QActivity.activity;
import static org.springframework.util.Assert.notNull;

/**
 * The type Activity service.
 */
@Service
@RequiredArgsConstructor
public class ActivityServiceImpl extends BaseController implements ActivitiesService {

    private final ActivityService activityService;

    private final ActivityOrderService activityOrderService;

    private final MemberWalletService memberWalletService;

    private final MemberService memberService;

    private final LocaleMessageSourceService messageSource;

    private final MiningOrderService miningOrderService;

    private final LockedOrderService lockedOrderService;

    private final MemberTransactionService memberTransactionService;

    private final CoinService coinService;

    private final SMSProvider smsProvider;

    @Value("${spark.system.md5.key}")
    private String md5Key;

    private final ActivityMapper activityMapper;

    @Override
    public MessageResult activityList(PageModel pageModel, String unit) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Predicate predicate = getPredicate(unit);
        Page<Activity> all = activityService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult lockedActivityList(Integer type, Integer step) {
        List<Activity> all = activityService.findByTypeAndStep(type, step); // Query the lock-up activity and is in progress
        return success(all);
    }

    @Override
    public MessageResult ExchangeCoinList(Activity activity) {
        activity.setCreateTime(DateUtil.getCurrentDate());
        activity = activityService.save(activity);
        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), activity);
    }

    @Override
    public MessageResult alterActivity(Long id, Integer progress) {
        notNull(id, "validate id!");

        Activity result = activityService.findOne(id);
        notNull(result, "validate activity!");

        if (result.getProgress() > progress.intValue()) {
            return error("The new progress value is less than the current value");
        }
        result.setProgress(progress);

        activityService.save(result);

        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult alterActivityFreezeAmount(Long id, BigDecimal freezeAmount) {
        notNull(id, "validate id!");

        Activity result = activityService.findOne(id);
        notNull(result, "validate activity!");

        if (result.getFreezeAmount().compareTo(freezeAmount) > 0) {
            return error("The number of newly frozen assets is less than the current value");
        }
        result.setFreezeAmount(freezeAmount);

        activityService.save(result);

        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult alterActivityTradedAmount(Long id, BigDecimal tradedAmount) {
        notNull(id, "validate id!");

        Activity result = activityService.findOne(id);
        notNull(result, "validate activity!");

        if (result.getTradedAmount().compareTo(tradedAmount) > 0) {
            return error("The number of newly frozen assets is less than the current value");
        }
        result.setTradedAmount(tradedAmount);

        activityService.save(result);

        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult alterActivity(ActivityRequest activityRequest, Admin admin) {
        String password = Encrypt.MD5(activityRequest.getPassword() + md5Key);
        Assert.isTrue(password.equals(admin.getPassword()), messageSource.getMessage("WRONG_PASSWORD"));

        Activity result = activityService.findOne(activityRequest.getId());

        notNull(result, "validate activity!");

        activityMapper.updateActivityFromRequest(activityRequest, result);
        activityService.saveAndFlush(result);
        return success(messageSource.getMessage("SUCCESS"));
    }

    @Override
    public MessageResult detail(Long id) {
        Activity activity = activityService.findById(id);
        notNull(activity, "validate id!");
        return success(activity);
    }

    @Override
    public MessageResult orderList(Long aid) {
        List<ActivityOrder> activityOrderList = activityOrderService.findAllByActivityId(aid);
        notNull(activityOrderList, "validate id!");
        return success(activityOrderList);
    }

    @Override
    public MessageResult distribute(Long oid) {
        ActivityOrder order = activityOrderService.findById(oid);
        if (order == null) {
            return error("The order does not exist");
        }
        if (order.getState() != 1) {
            return error("Order status error (non-to-distributed status)!");
        }
        Activity activity = activityService.findById(order.getActivityId());
        if (activity == null) {
            return error("The activity does not exist");
        }
        // Types of activities 1, 2, 3, and 4 need to determine whether they are in the distribution stage
        if (activity.getType() == 1 || activity.getType() == 2 || activity.getType() == 3 || activity.getType() == 4) {
            // Whether the event ends
            if (activity.getStep() != 2) {
                return error("The event has not ended yet");
            }
        }

        // type = 3 (position sharing)
        // Detailed explanation of the position sharing activity: Users can divide the amount of coins in the pool without spending any coins, and divide them according to the position ratio
        if (activity.getType() == 3) {
            // Unfreeze accept coin (acceptUnit)
            MemberWallet freezeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getAcceptUnit(), order.getMemberId());
            if (freezeWallet == null) {
                return error("The frozen coin wallet does not exist");
            }
            memberWalletService.thawBalance(freezeWallet, order.getFreezeAmount());

            // Distribute active coins (unit) (get active coins wallet)
            MemberWallet distributeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getUnit(), order.getMemberId());

            if (distributeWallet == null) {
                return error("The activity coin wallet does not exist");
            }
            // Number of distributions = Positions / Total Positions * Active Supply
            BigDecimal disAmount = order.getFreezeAmount().divide(activity.getFreezeAmount()).multiply(activity.getTotalSupply()).setScale(activity.getAmountScale(), BigDecimal.ROUND_HALF_DOWN);
            // User wallet increases assets
            memberWalletService.increaseBalance(distributeWallet.getId(), disAmount);

            MemberTransaction memberTransaction = new MemberTransaction();
            memberTransaction.setFee(BigDecimal.ZERO);
            memberTransaction.setAmount(disAmount);
            memberTransaction.setMemberId(distributeWallet.getMemberId());
            memberTransaction.setSymbol(activity.getUnit());
            memberTransaction.setType(TransactionType.ACTIVITY_BUY);
            memberTransaction.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction.setRealFee("0");
            memberTransaction.setDiscountFee("0");
            memberTransaction = memberTransactionService.save(memberTransaction);

            Member member = memberService.findOne(order.getMemberId());
            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear users, you participated in the event to redeemed" + disAmount + activity.getUnit() + "Account has been received!");
            } catch (Exception e) {
                return error(e.getMessage());
            }

            // Update order status
            order.setState(2);// Delivered
            order.setAmount(disAmount); // Transaction quantity
            activityOrderService.saveAndFlush(order);

            return success("The distribution of the holdings and sharing activities is completed, and this order is distributed:" + disAmount);
        }

        // type = 4 (free subscription)
        if (activity.getType() == 4) {
            // Deduct acceptance coins
            MemberWallet freezeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getAcceptUnit(), order.getMemberId());
            if (freezeWallet == null) {
                return error("The frozen coin wallet does not exist");
            }
            memberWalletService.decreaseFrozen(freezeWallet.getId(), order.getTurnover());

            MemberTransaction memberTransaction1 = new MemberTransaction();
            memberTransaction1.setFee(BigDecimal.ZERO);
            memberTransaction1.setAmount(order.getTurnover().negate());
            memberTransaction1.setMemberId(freezeWallet.getMemberId());
            memberTransaction1.setSymbol(activity.getAcceptUnit());
            memberTransaction1.setType(TransactionType.ACTIVITY_BUY);
            memberTransaction1.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction1.setRealFee("0");
            memberTransaction1.setDiscountFee("0");
            memberTransaction1 = memberTransactionService.save(memberTransaction1);

            // Distribute active coins
            BigDecimal disAmount = order.getAmount();
            MemberWallet distributeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getUnit(), order.getMemberId());
            if (distributeWallet == null) {
                return error("The activity coin wallet does not exist");
            }
            memberWalletService.increaseBalance(distributeWallet.getId(), disAmount);

            MemberTransaction memberTransaction = new MemberTransaction();
            memberTransaction.setFee(BigDecimal.ZERO);
            memberTransaction.setAmount(disAmount);
            memberTransaction.setMemberId(distributeWallet.getMemberId());
            memberTransaction.setSymbol(activity.getUnit());
            memberTransaction.setType(TransactionType.ACTIVITY_BUY);
            memberTransaction.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction.setRealFee("0");
            memberTransaction.setDiscountFee("0");
            memberTransaction = memberTransactionService.save(memberTransaction);

            // Update order status
            order.setState(2);// Delivered
            activityOrderService.saveAndFlush(order);

            Member member = memberService.findOne(order.getMemberId());
            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear users, you participated in the event to redeemed" + disAmount + activity.getUnit() + "Account has been received!");
            } catch (Exception e) {
                return error(e.getMessage());
            }

            return success("The free subscription activity is distributed and this order is distributed:" + disAmount);
        }

        // Cloud mining machine sales type
        if (activity.getType() == 5) {
            // Deduct acceptance coins
            MemberWallet freezeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getAcceptUnit(), order.getMemberId());
            if (freezeWallet == null) {
                return error("The frozen coin wallet does not exist");
            }
            memberWalletService.decreaseFrozen(freezeWallet.getId(), order.getTurnover());

            MemberTransaction memberTransaction1 = new MemberTransaction();
            memberTransaction1.setFee(BigDecimal.ZERO);
            memberTransaction1.setAmount(order.getTurnover().negate());
            memberTransaction1.setMemberId(freezeWallet.getMemberId());
            memberTransaction1.setSymbol(activity.getAcceptUnit());
            memberTransaction1.setType(TransactionType.ACTIVITY_BUY);
            memberTransaction1.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction1.setRealFee("0");
            memberTransaction1.setDiscountFee("0");
            memberTransaction1 = memberTransactionService.save(memberTransaction1);

            // Update order status
            order.setState(2);// Delivered
            activityOrderService.saveAndFlush(order);

            // Generate miners
            for (int i = 0; i < order.getAmount().intValue(); i++) {
                Date currentDate = DateUtil.getCurrentDate();
                MiningOrder mo = new MiningOrder();
                mo.setActivityId(activity.getId());
                mo.setMemberId(order.getMemberId());
                mo.setMiningDays(activity.getMiningDays());
                mo.setMiningDaysprofit(activity.getMiningDaysprofit());
                mo.setMiningUnit(activity.getMiningUnit());
                mo.setCurrentDaysprofit(activity.getMiningDaysprofit());
                mo.setCreateTime(currentDate);
                mo.setEndTime(DateUtil.dateAddDay(currentDate, activity.getMiningDays()));
                mo.setImage(activity.getSmallImageUrl());
                mo.setTitle(activity.getTitle());
                mo.setMiningStatus(1); // Mining status (1: mining)
                mo.setMiningedDays(0); // Initial is 0 days
                mo.setTotalProfit(BigDecimal.ZERO);
                mo.setType(0); // General mining machines
                mo.setMiningInvite(activity.getMiningInvite()); // invite
                mo.setMiningInvitelimit(activity.getMiningInvitelimit()); // Capacity limit
                mo.setPeriod(activity.getMiningPeriod()); // Mining output cycle
                miningOrderService.save(mo);
            }
            Member member = memberService.findOne(order.getMemberId());
            // Can invite increase the production capacity?
            // (Inviting one person to participate can only increase the production capacity of one mining machine)
            if (activity.getMiningInvite().compareTo(BigDecimal.ZERO) > 0) {
                if (member != null) {
                    if (member.getInviterId() != null) {
                        Member inviter = memberService.findOne(member.getInviterId());
                        List<MiningOrder> miningOrders = miningOrderService.findAllByMemberIdAndActivityId(inviter.getId(), activity.getId());
                        if (miningOrders.size() > 0) {
                            for (MiningOrder item : miningOrders) {
                                // If the current capacity is lower than the limit capacity
                                if (item.getCurrentDaysprofit().subtract(item.getMiningDaysprofit()).divide(item.getMiningDaysprofit()).compareTo(activity.getMiningInvitelimit()) < 0) {
                                    // Obtain new production capacity
                                    BigDecimal newMiningDaysprofit = item.getCurrentDaysprofit().add(item.getMiningDaysprofit().multiply(activity.getMiningInvite()));
                                    // If the new capacity is higher than the limit capacity
                                    if (newMiningDaysprofit.compareTo(item.getMiningDaysprofit().add(item.getMiningDaysprofit().multiply(activity.getMiningInvitelimit()))) > 0) {
                                        newMiningDaysprofit = item.getMiningDaysprofit().add(item.getMiningDaysprofit().multiply(activity.getMiningInvitelimit()));
                                    }
                                    item.setCurrentDaysprofit(newMiningDaysprofit);
                                    miningOrderService.save(item);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, the cloud mining machine you purchased has been successfully deployed!");
            } catch (Exception e) {
                return error(e.getMessage());
            }
            return success("The mining machine was successfully deployed");
        }

        // Cloud mining machine sales type
        if (activity.getType() == 6) {
            if (activity.getReleaseTimes().compareTo(BigDecimal.ZERO) <= 0 || activity.getReleaseTimes() == null) {
                return error("The release multiple cannot be 0!");
            }
            // Deduct acceptance coins
            MemberWallet freezeWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getAcceptUnit(), order.getMemberId());
            if (freezeWallet == null) {
                return error("The frozen coin wallet does not exist");
            }
            // Deducted the number of locked positions and threshold fees
            memberWalletService.decreaseFrozen(freezeWallet.getId(), order.getTurnover());

            MemberTransaction memberTransaction1 = new MemberTransaction();
            memberTransaction1.setFee(BigDecimal.ZERO);
            memberTransaction1.setAmount(order.getTurnover().negate());
            memberTransaction1.setMemberId(freezeWallet.getMemberId());
            memberTransaction1.setSymbol(activity.getAcceptUnit());
            memberTransaction1.setType(TransactionType.ACTIVITY_BUY);
            memberTransaction1.setCreateTime(DateUtil.getCurrentDate());
            memberTransaction1.setRealFee("0");
            memberTransaction1.setDiscountFee("0");
            memberTransaction1 = memberTransactionService.save(memberTransaction1);

            // ToRelease Increase the balance to be released (= number of user participation * multiple of release)
            memberWalletService.increaseToRelease(freezeWallet.getId(), order.getAmount().multiply(activity.getReleaseTimes()));

            // Update order status
            order.setState(2);// Delivered
            activityOrderService.saveAndFlush(order);

            // Generate locked orders
            Date currentDate = DateUtil.getCurrentDate();
            LockedOrder lo = new LockedOrder();
            lo.setActivityId(activity.getId());
            lo.setMemberId(order.getMemberId());
            lo.setLockedDays(activity.getLockedDays());
            lo.setReleasedDays(0);
            lo.setReleaseUnit(activity.getLockedUnit());
            lo.setReleaseType(activity.getReleaseType());
            lo.setPeriod(activity.getLockedPeriod());
            lo.setLockedStatus(1); // Locked state: Released
            lo.setReleasePercent(activity.getReleasePercent());
            lo.setReleaseCurrentpercent(activity.getReleasePercent());
            lo.setImage(activity.getSmallImageUrl());
            lo.setTitle(activity.getTitle());
            lo.setTotalLocked(order.getAmount().multiply(activity.getReleaseTimes()));
            lo.setReleaseTimes(activity.getReleaseTimes());
            lo.setOriginReleaseamount(order.getAmount().multiply(activity.getReleaseTimes()).divide(BigDecimal.valueOf(activity.getLockedDays()), 8, BigDecimal.ROUND_HALF_DOWN));
            lo.setCurrentReleaseamount(order.getAmount().multiply(activity.getReleaseTimes()).divide(BigDecimal.valueOf(activity.getLockedDays()), 8, BigDecimal.ROUND_HALF_DOWN));
            lo.setTotalRelease(BigDecimal.ZERO);
            lo.setLockedInvite(activity.getMiningInvite());
            lo.setLockedInvitelimit(activity.getMiningInvitelimit());

            // Release with a day cycle
            if (activity.getLockedPeriod() == 0)
                lo.setEndTime(DateUtil.dateAddDay(currentDate, activity.getLockedDays()));
            // Release with a cycle
            if (activity.getLockedPeriod() == 1)
                lo.setEndTime(DateUtil.dateAddDay(currentDate, activity.getLockedDays() * 7));
            // Release of the month cycle
            if (activity.getLockedPeriod() == 2)
                lo.setEndTime(DateUtil.dateAddMonth(currentDate, activity.getLockedDays()));
            // Release with years
            if (activity.getLockedPeriod() == 3)
                lo.setEndTime(DateUtil.dateAddYear(currentDate, activity.getLockedDays()));

            lockedOrderService.save(lo);

            Member member = memberService.findOne(order.getMemberId());
            // Can invite increase the production capacity?
            // (Inviting one person to participate can only increase the production capacity of one mining machine)
            if (activity.getMiningInvite().compareTo(BigDecimal.ZERO) > 0) {
                if (member != null) {
                    if (member.getInviterId() != null) {
                        Member inviter = memberService.findOne(member.getInviterId());
                        List<LockedOrder> lockedOrders = lockedOrderService.findAllByMemberIdAndActivityId(inviter.getId(), activity.getId());
                        if (lockedOrders.size() > 0) {
                            for (LockedOrder item : lockedOrders) {
                                // If the current capacity is lower than the limit capacity
                                if (item.getCurrentReleaseamount().subtract(item.getOriginReleaseamount()).divide(item.getOriginReleaseamount()).compareTo(activity.getMiningInvitelimit()) < 0) {
                                    // Obtain new production capacity
                                    BigDecimal newReleaseAmount = item.getCurrentReleaseamount().add(item.getCurrentReleaseamount().multiply(activity.getMiningInvite()));
                                    // If the new capacity is higher than the limit capacity
                                    if (newReleaseAmount.compareTo(item.getOriginReleaseamount().add(item.getOriginReleaseamount().multiply(activity.getMiningInvitelimit()))) > 0) {
                                        newReleaseAmount = item.getOriginReleaseamount().add(item.getOriginReleaseamount().multiply(activity.getMiningInvitelimit()));
                                    }
                                    item.setCurrentReleaseamount(newReleaseAmount);
                                    lockedOrderService.save(item);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            try {
                smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear users, the lock-up activity you participated in has passed the review!");
            } catch (Exception e) {
                return error(e.getMessage());
            }
            return success("Successful review");
        }

        if (activity.getType() == 6) {

        }

        return error("Unknown activity type");
    }

    @Override
    public MessageResult lockMemberCoin(Long memberId, Long activityId, String unit, BigDecimal amount) {
        // Check if the user exists
        Member member = memberService.findOne(memberId);
        notNull(member, "user!");

        // Check if the activity exists
        Activity activity = activityService.findOne(activityId);
        notNull(activity, "This activity does not exist!");
        // Check if the activity is in progress
        if (activity.getType() != 6) {
            return MessageResult.error("Activities are not locked activities!");
        }
        // Check if the activity is in progress
        if (activity.getStep() != 1) {
            return MessageResult.error("The activity is not in progress!");
        }

        // Minimum starting volume/minimum locked volume
        if (activity.getMinLimitAmout().compareTo(BigDecimal.ZERO) > 0) {
            if (activity.getMinLimitAmout().compareTo(amount) > 0) {
                return MessageResult.error("It cannot be lower than the minimum starting volume!");
            }
        }
        if (activity.getMaxLimitAmout().compareTo(BigDecimal.ZERO) > 0 || activity.getLimitTimes() > 0) {
            // Maximum redemption volume/maximum locked volume (first get the amount of orders placed)
            List<ActivityOrder> orderDetailList = activityOrderService.findAllByActivityIdAndMemberId(member.getId(), activityId);
            BigDecimal alreadyAttendAmount = BigDecimal.ZERO;
            int alreadyAttendTimes = 0;
            if (orderDetailList != null) {
                alreadyAttendTimes = orderDetailList.size();
                for (int i = 0; i < orderDetailList.size(); i++) {
                    if (activity.getType() == 3) {
                        alreadyAttendAmount = alreadyAttendAmount.add(orderDetailList.get(i).getFreezeAmount());
                    } else {
                        alreadyAttendAmount = alreadyAttendAmount.add(orderDetailList.get(i).getAmount());
                    }
                }
            }
            // Maximum purchase limit
            if (activity.getMaxLimitAmout().compareTo(BigDecimal.ZERO) > 0) {
                if (alreadyAttendAmount.add(amount).compareTo(activity.getMaxLimitAmout()) > 0) {
                    return MessageResult.error("The maximum redemption volume cannot be exceeded!");
                }
            }
            // Personal purchase limit
            if (activity.getLimitTimes() > 0) {
                if (activity.getLimitTimes() < alreadyAttendTimes + 1) {
                    return MessageResult.error("Exceed purchase limit!");
                }
            }
        }

        // Check position requirements
        if (activity.getHoldLimit().compareTo(BigDecimal.ZERO) > 0 && activity.getHoldUnit() != null && activity.getHoldUnit() != "") {
            MemberWallet holdCoinWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getHoldUnit(), member.getId());
            if (holdCoinWallet == null) {
                return MessageResult.error("The wallet requires that the holding requires that the wallet does not exist!");
            }
            if (holdCoinWallet.getIsLock().equals(BooleanEnum.IS_TRUE)) {
                return MessageResult.error("The wallet is locked!");
            }
            if (holdCoinWallet.getBalance().compareTo(activity.getHoldLimit()) < 0) {
                return MessageResult.error("Yours" + activity.getHoldUnit() + "The number of positions does not meet the conditions!");
            }
        }

        // Check if the currency exists
        Coin coin;
        coin = coinService.findByUnit(activity.getAcceptUnit());
        if (coin == null) {
            return MessageResult.error("The currency does not exist");
        }

        // Check if the wallet is available
        MemberWallet acceptCoinWallet = memberWalletService.findByCoinUnitAndMemberId(activity.getAcceptUnit(), member.getId());
        if (acceptCoinWallet == null || acceptCoinWallet == null) {
            return MessageResult.error("User wallet does not exist");
        }
        if (acceptCoinWallet.getIsLock().equals(BooleanEnum.IS_TRUE)) {
            return MessageResult.error("The wallet is locked");
        }

        // Check if the balance is sufficient
        BigDecimal totalAcceptCoinAmount = BigDecimal.ZERO;
        totalAcceptCoinAmount = amount.add(activity.getLockedFee()).setScale(activity.getAmountScale(), BigDecimal.ROUND_HALF_DOWN);

        if (acceptCoinWallet.getBalance().compareTo(totalAcceptCoinAmount) < 0) {
            return MessageResult.error("Insufficient user balance");
        }

        ActivityOrder activityOrder = new ActivityOrder();
        activityOrder.setActivityId(activityId);
        activityOrder.setAmount(amount); // Actual locked number
        activityOrder.setFreezeAmount(totalAcceptCoinAmount); // The frozen assets here include the actual number of users locked positions and threshold fees.
        activityOrder.setBaseSymbol(activity.getAcceptUnit());
        activityOrder.setCoinSymbol(activity.getUnit());
        activityOrder.setCreateTime(DateUtil.getCurrentDate());
        activityOrder.setMemberId(member.getId());
        activityOrder.setPrice(activity.getPrice());
        activityOrder.setState(1); // Not delivered
        activityOrder.setTurnover(totalAcceptCoinAmount);// As the standard for asset freezing or deduction, this project is used as the number of participation in the lock-up activity.
        activityOrder.setType(activity.getType());

        MessageResult mr = activityOrderService.saveActivityOrder(member.getId(), activityOrder);

        if (mr.getCode() != 0) {
            return MessageResult.error(500, "Event participation failed:" + mr.getMessage());
        } else {
            return MessageResult.success("The locked position is submitted successfully, please go to the event management to agree to lock the position!");
        }
    }

    private Predicate getPredicate(String unit) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (unit != null) {
            booleanExpressions.add(activity.acceptUnit.like(unit + "%"));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }
}
