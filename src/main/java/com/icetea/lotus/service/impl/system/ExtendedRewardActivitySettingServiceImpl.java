package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.ActivityRewardType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.QRewardActivitySetting;
import com.icetea.lotus.entity.spot.RewardActivitySetting;
import com.icetea.lotus.service.RewardActivitySettingService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedRewardActivitySettingService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * The type Extended reward activity setting service.
 */
@Slf4j
@Service
public class ExtendedRewardActivitySettingServiceImpl extends BaseAdminController implements ExtendedRewardActivitySettingService {

    private final RewardActivitySettingService rewardActivitySettingService;

    public ExtendedRewardActivitySettingServiceImpl(BaseAdminService baseAdminService, RewardActivitySettingService rewardActivitySettingService) {
        super(baseAdminService);
        this.rewardActivitySettingService = rewardActivitySettingService;
    }

    @Override
    public MessageResult merge(RewardActivitySetting setting) {
        rewardActivitySettingService.save(setting);
        return MessageResult.success("Save successfully");
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, ActivityRewardType type) {
        BooleanExpression predicate = null;
        if (type != null) {
            predicate = QRewardActivitySetting.rewardActivitySetting.type.eq(type);
        }
        Page<RewardActivitySetting> all = rewardActivitySettingService.findAll(predicate, pageModel);
        return success(all);
    }

    @Override
    public MessageResult deletes(Long[] ids) {
        Assert.notNull(ids, "ids cannot be null");
        rewardActivitySettingService.deletes(ids);
        return MessageResult.success("Delete successfully");
    }
}
