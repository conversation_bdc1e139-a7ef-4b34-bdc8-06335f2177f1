package com.icetea.lotus.service.impl.transaction;

import com.icetea.lotus.client.WalletClient;
import com.icetea.lotus.dto.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.AdminHistoryWithdrawResponse;
import com.icetea.lotus.service.transaction.TransactionManagementService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionManagementServiceImpl implements TransactionManagementService {
    private final WalletClient walletClient;

    @Override
    public Page<AdminHistoryWithdrawResponse> getAllWithdrawHistory(AdminHistoryWithdrawRequest adminHistoryWithdrawRequest) {
        try {
            return walletClient.getAllWithdrawHistory(adminHistoryWithdrawRequest);
        } catch (FeignException e) {
            log.error("Error fetching withdraw history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch withdraw history", e);
        }
    }

}
