package com.icetea.lotus.service.impl.otc;

import com.icetea.lotus.constant.OrderStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Order;
import com.icetea.lotus.entity.spot.QOrder;
import com.icetea.lotus.model.screen.OrderScreen;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.OrderService;
import com.icetea.lotus.service.otc.AdminOrderService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.OtcOrderVO;
import com.querydsl.core.types.Predicate;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Admin order service.
 */
@Service
@RequiredArgsConstructor
public class AdminOrderServiceImpl extends BaseController implements AdminOrderService {

    private final OrderService orderService;

    private final LocaleMessageSourceService messageSource;

    @Override
    public MessageResult all() {
        List<Order> exchangeOrderList = orderService.findAll();
        if (exchangeOrderList != null && exchangeOrderList.size() > 0) {
            return success(exchangeOrderList);
        }
        return error(messageSource.getMessage("NO_DATA"));
    }

    @Override
    public MessageResult detail(Long id) {
        Order one = orderService.findOne(id);
        if (one == null) {
            return error(messageSource.getMessage("NO_DATA"));
        }
        return success(one);
    }

    @Override
    public MessageResult status(Long id, OrderStatus status) {
        Order order = orderService.findOne(id);
        notNull(order, "validate order.id!");
        order.setStatus(status);
        orderService.save(order);
        return success();
    }

    @Override
    public MessageResult page(PageModel pageModel, OrderScreen screen) {
        List<Predicate> predicate = getPredicates(screen);
        Page<OtcOrderVO> page = orderService.outExcel(predicate,pageModel);
        // Page<Order> all = orderService.findAll(predicate, pageModel.getPageable());
        return success(page);
    }

    private List<Predicate> getPredicates(OrderScreen screen) {
        ArrayList<Predicate> predicates = new ArrayList<>();
        // predicates.add(QOrder.order.status.ne(OrderStatus.CANCELLED));
        if (StringUtils.isNotBlank(screen.getOrderSn())) {
            predicates.add(QOrder.order.orderSn.eq(screen.getOrderSn()));
        }
        if (screen.getStartTime() != null) {
            predicates.add(QOrder.order.createTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null){
            predicates.add(QOrder.order.createTime.lt(DateUtil.dateAddDay(screen.getEndTime(),1)));
        }
        if (screen.getStatus() != null) {
            predicates.add(QOrder.order.status.eq(screen.getStatus()));
        }
        if (StringUtils.isNotEmpty(screen.getUnit())) {
            predicates.add(QOrder.order.coin.unit.equalsIgnoreCase(screen.getUnit()));
        }
        if (StringUtils.isNotBlank(screen.getMemberName())) {
            predicates.add(QOrder.order.memberName.like("%" + screen.getMemberName() + "%")
                    .or(QOrder.order.memberRealName.like("%" + screen.getMemberName() + "%")));
        }
        if (StringUtils.isNotBlank(screen.getCustomerName())) {
            predicates.add(QOrder.order.customerName.like("%" + screen.getCustomerName() + "%")
                    .or(QOrder.order.customerRealName.like("%" + screen.getCustomerName() + "%")));
        }
        if(screen.getMinMoney()!=null) {
            predicates.add(QOrder.order.money.goe(screen.getMinMoney()));
        }
        if(screen.getMaxMoney()!=null) {
            predicates.add(QOrder.order.money.loe(screen.getMaxMoney()));
        }
        if(screen.getMinNumber()!=null) {
            predicates.add(QOrder.order.number.goe(screen.getMinNumber()));
        }
        if(screen.getMaxNumber()!=null) {
            predicates.add(QOrder.order.number.loe(screen.getMaxNumber()));
        }
        if (screen.getAdvertiseType() != null){
            predicates.add(QOrder.order.advertiseType.eq(screen.getAdvertiseType()));
        }
        return /*PredicateUtils.getPredicate(booleanExpressions)*/predicates;
    }

    @Override
    public MessageResult getOrderNum() {
        return orderService.getOrderNum();
    }

    @Override
    public void outExcel(PageModel pageModel, OrderScreen screen, HttpServletResponse response) throws IOException {
        List<OtcOrderVO> list = orderService.outExcel(getPredicates(screen),pageModel).getContent();
        ExcelUtil.listToExcel(list,OtcOrderVO.class.getDeclaredFields(),response.getOutputStream());
    }
}
