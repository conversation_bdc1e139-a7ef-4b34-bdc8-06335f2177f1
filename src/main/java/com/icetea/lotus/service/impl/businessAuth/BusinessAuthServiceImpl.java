package com.icetea.lotus.service.impl.businessAuth;

import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.BusinessAuthApply;
import com.icetea.lotus.entity.spot.BusinessAuthDeposit;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.QBusinessAuthApply;
import com.icetea.lotus.entity.spot.QBusinessAuthDeposit;
import com.icetea.lotus.service.BusinessAuthApplyService;
import com.icetea.lotus.service.BusinessAuthDepositService;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.businessAuth.BusinessAuthService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Business auth service.
 */
@Service
@RequiredArgsConstructor
public class BusinessAuthServiceImpl extends BaseController implements BusinessAuthService {

    private final BusinessAuthDepositService businessAuthDepositService;
    private final CoinService coinService;
    private final BusinessAuthApplyService businessAuthApplyService;

    @Override
    public MessageResult getAll(PageModel pageModel, CommonStatus status) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        // get static
        QBusinessAuthDeposit businessAuthDeposit = QBusinessAuthDeposit.businessAuthDeposit;
        //check status
        if (status != null) {
            // add value in Array
            booleanExpressions.add(businessAuthDeposit.status.eq(status));
        }
        //get value
        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<BusinessAuthDeposit> depositPage = businessAuthDepositService.findAll(predicate, pageModel);
        MessageResult result = MessageResult.success();
        result.setData(depositPage);
        return result;
    }

    @Override
    public MessageResult create(Admin admin, Double amount, String coinUnit) {
        Coin coin = coinService.findByUnit(coinUnit);
        if (coin == null) {
            return error("validate coinUnit");
        }
        BusinessAuthDeposit businessAuthDeposit = new BusinessAuthDeposit();
        businessAuthDeposit.setAmount(new BigDecimal(amount));
        businessAuthDeposit.setCoin(coin);
        businessAuthDeposit.setCreateTime(new Date());
        businessAuthDeposit.setAdmin(admin);
        businessAuthDeposit.setStatus(CommonStatus.NORMAL);
        businessAuthDepositService.save(businessAuthDeposit);
        return success();
    }

    @Override
    public MessageResult detail(Long id) {
        MessageResult result = businessAuthApplyService.detail(id);
        return result;
    }

    @Override
    public MessageResult update(Long id, Double amount, CommonStatus status) {
        // find by id
        BusinessAuthDeposit oldData = businessAuthDepositService.findById(id);
        if (amount != null) {
            /*if(businessAuthDeposit.getAmount().compareTo(oldData.getAmount())>0){
                //如果上调了保证金，所有使用当前类型保证金的已认证商家的认证状态都改为保证金不足
                ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
                booleanExpressions.add(QDepositRecord.depositRecord.coin.eq(oldData.getCoin()));
                booleanExpressions.add(QDepositRecord.depositRecord.status.eq(DepositStatusEnum.PAY));
                Predicate predicate=PredicateUtils.getPredicate(booleanExpressions);
                List<DepositRecord> depositRecordList=depositRecordService.findAll(predicate);
                if(depositRecordList!=null){
                    List<Long> idList=new ArrayList<>();
                    for(DepositRecord depositRecord:depositRecordList){
                        idList.add(depositRecord.getMember().getId());
                    }
                    memberService.updateCertifiedBusinessStatusByIdList(idList);
                }
            }*/
            //update amount
            oldData.setAmount(new BigDecimal(amount));
        }
        // update status if not null
        if (status != null) {
            oldData.setStatus(status);
        }
        // save in db
        businessAuthDepositService.save(oldData);
        return success();
    }

    @Override
    public MessageResult page(PageModel pageModel, CertifiedBusinessStatus status, String account) {
        List<BooleanExpression> lists = new ArrayList<>();
        lists.add(QBusinessAuthApply.businessAuthApply.member.certifiedBusinessStatus.ne(CertifiedBusinessStatus.NOT_CERTIFIED));
        if (!"".equals(account)) {
            lists.add(QBusinessAuthApply.businessAuthApply.member.username.like("%" + account + "%")
                    .or(QBusinessAuthApply.businessAuthApply.member.mobilePhone.like(account + "%"))
                    .or(QBusinessAuthApply.businessAuthApply.member.email.like(account + "%"))
                    .or(QBusinessAuthApply.businessAuthApply.member.realName.like("%" + account + "%")));
        }
        if (status != null) {
            lists.add(QBusinessAuthApply.businessAuthApply.certifiedBusinessStatus.eq(status));
        }
        Page<BusinessAuthApply> page = businessAuthApplyService.page(PredicateUtils.getPredicate(lists), pageModel.getPageable());
        return success(page);
    }

    @Override
    public MessageResult getSearchStatus() {
        CertifiedBusinessStatus[] statuses = CertifiedBusinessStatus.values();
        List<Map> list = new ArrayList<>();
        for (CertifiedBusinessStatus status : statuses) {
            if (status == CertifiedBusinessStatus.NOT_CERTIFIED
                    || status.getOrdinal() >= CertifiedBusinessStatus.DEPOSIT_LESS.getOrdinal()) {
                continue;
            }
            Map map = new HashMap();
            map.put("name", status.getName());
            map.put("value", status.getOrdinal());
            list.add(map);
        }
        return success(list);
    }
}
