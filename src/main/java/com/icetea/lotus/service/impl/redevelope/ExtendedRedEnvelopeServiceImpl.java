package com.icetea.lotus.service.impl.redevelope;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.RedEnvelope;
import com.icetea.lotus.entity.spot.RedEnvelopeDetail;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.RedEnvelopeDetailService;
import com.icetea.lotus.service.RedEnvelopeService;
import com.icetea.lotus.service.redenvelope.ExtendedRedEnvelopeService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Extended red envelope service.
 */
@Service
@RequiredArgsConstructor
public class ExtendedRedEnvelopeServiceImpl extends BaseController implements ExtendedRedEnvelopeService {

    private final RedEnvelopeService redEnveloperService;

    private final RedEnvelopeDetailService redEnveloperDetailService;

    private final LocaleMessageSourceService messageSource;

    private final CoinService coinService;

    @Override
    public MessageResult envelopeList(PageModel pageModel) {

        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<RedEnvelope> all = redEnveloperService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult envelopeDetail(Long id) {
        RedEnvelope redEnvelope = redEnveloperService.findOne(id);
        Assert.notNull(redEnvelope, "validate id!");
        return success(redEnvelope);
    }

    @Override
    public MessageResult envelopeDetailList(Long envelopeId, Integer pageNo, Integer pageSize) {
        Page<RedEnvelopeDetail> detailList = redEnveloperDetailService.findByEnvelope(envelopeId, pageNo, pageSize);

        return success(detailList);
    }

    @Override
    public MessageResult addRedEnvelope(RedEnvelope redEnvelope) {
        // Check if the currency exists
        Coin coin = coinService.findByUnit(redEnvelope.getUnit());
        Assert.notNull(coin, "Invalid currency!");

        // Generate red envelope number
        SimpleDateFormat f = new SimpleDateFormat("MMddHHmmss");
        redEnvelope.setEnvelopeNo(f.format(new Date()) + GeneratorUtil.getNonceString(5).toUpperCase());

        redEnvelope.setMemberId(1L); // Users issued by the platform with fixed 1
        redEnvelope.setPlateform(1); // The fixed platform issuance is 1 (platform red envelope)
        redEnvelope.setState(0);
        redEnvelope.setReceiveAmount(BigDecimal.ZERO);
        redEnvelope.setReceiveCount(0);

        redEnvelope.setCreateTime(DateUtil.getCurrentDate());
        redEnvelope = redEnveloperService.save(redEnvelope);
        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), redEnvelope);
    }

    @Override
    public MessageResult modifyRedEnvelope(Long id, Integer type, Integer invite, String unit, BigDecimal maxRand, BigDecimal totalAmount, Integer count, String logoImage, String bgImage, String name, String detail, Integer expiredHours, Integer state) {

        RedEnvelope redEnvelope = redEnveloperService.findOne(id);
        notNull(redEnvelope, "Validate Red Envelope!");

        if(type != null) redEnvelope.setType(type);
        if(invite != null) redEnvelope.setInvite(invite);
        if(unit != null) {
            // Check if the currency exists
            Coin coin = coinService.findByUnit(redEnvelope.getUnit());
            Assert.notNull(coin, "Invalid currency!");
            redEnvelope.setUnit(unit);
        };
        if(maxRand != null) redEnvelope.setMaxRand(maxRand);
        if(totalAmount != null) redEnvelope.setTotalAmount(totalAmount);
        if(count != null) redEnvelope.setCount(count);
        if(logoImage != null) redEnvelope.setLogoImage(logoImage);
        if(bgImage != null) redEnvelope.setBgImage(bgImage);
        if(name != null) redEnvelope.setName(name);
        if(detail != null) redEnvelope.setDetail(detail);
        if(expiredHours != null) redEnvelope.setExpiredHours(expiredHours);
        if(state != null) redEnvelope.setState(state);

        redEnvelope = redEnveloperService.save(redEnvelope);

        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), redEnvelope);
    }
}
