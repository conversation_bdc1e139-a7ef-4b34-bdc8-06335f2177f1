package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.DataDictionary;
import com.icetea.lotus.model.create.DataDictionaryCreate;
import com.icetea.lotus.model.update.DataDictionaryUpdate;
import com.icetea.lotus.service.DataDictionaryService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedDataDictionaryService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * The type Extended data dictionary service.
 */
@Slf4j
@Service
public class ExtendedDataDictionaryServiceImpl extends BaseAdminController implements ExtendedDataDictionaryService {

    private final DataDictionaryService service;
    private final KafkaTemplate<String, String> kafkaTemplate;

    public ExtendedDataDictionaryServiceImpl(BaseAdminService baseAdminService, DataDictionaryService service, KafkaTemplate<String, String> kafkaTemplate) {
        super(baseAdminService);
        this.service = service;
        this.kafkaTemplate = kafkaTemplate;
    }


    @Override
    public MessageResult post(DataDictionaryCreate model) {
        DataDictionary data = service.findByBond(model.getBond());
        if (data != null) {
            return error("bond already existed!");
        }
        service.save(model);
        kafkaTemplate.send("data-dictionary-save-update", model.getBond(), model.getValue());
        log.info(">>>>>> New end >>>>>");
        return success();
    }

    @Override
    public MessageResult page(PageModel pageModel) {
        Page<DataDictionary> all = service.findAll(null, pageModel);
        return success(all);
    }

    @Override
    public MessageResult put(String bond, DataDictionaryUpdate model) {
        DataDictionary dataDictionary = service.findByBond(bond);
        Assert.notNull(dataDictionary, "validate bond");
        service.update(model, dataDictionary);
        dataDictionary.setValue(model.getValue());
        dataDictionary.setComment(model.getComment());
        kafkaTemplate.send("data-dictionary-save-update", bond, dataDictionary.getValue());
        return success();
    }
}
