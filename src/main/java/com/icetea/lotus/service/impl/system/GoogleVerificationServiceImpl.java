package com.icetea.lotus.service.impl.system;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.GoogleVerificationService;
import com.icetea.lotus.util.GoogleAuthenticatorUtil;
import com.icetea.lotus.util.Md5;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * The type Google verification service.
 */
@Slf4j
@Service
public class GoogleVerificationServiceImpl extends BaseAdminController implements GoogleVerificationService {

    private final MemberService memberService;

    @Value("${google.host}")
    private String googleHost;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public GoogleVerificationServiceImpl(BaseAdminService baseAdminService, MemberService memberService) {
        super(baseAdminService);
        this.memberService = memberService;
    }

    @Override
    public MessageResult yzgoogle(AuthMember user, String codes) {
        // enter the code shown on device. Edit this and run it fast before the
        // code expires!
        long code = Long.parseLong(codes);
        Member member = memberService.findOne(user.getId());
        long t = System.currentTimeMillis();
        GoogleAuthenticatorUtil ga = new GoogleAuthenticatorUtil();
        // ga.setWindowSize(0); // should give 5 * 30 seconds of grace...
        boolean r = ga.checkCode(member.getGoogleKey(), code, t);
        System.out.println("rrrr="+r);
        if(!r){
            return MessageResult.error("Validation failed");
        }
        else{
            return MessageResult.success("Verification passed");
        }
    }

    @Override
    public MessageResult sendgoogle(AuthMember user) {
         /*for(int i = 0;i<50;i++){
            log.info("######################       开始循环次数={}    ######################",i+1);
            GoogleAuthenticatorUtil.generateSecretKey();
            log.info("######################       结束循环次数={}    ######################",i+1);
        }*/
        log.info("Start entering user id={}",user.getId());
        long current = System.currentTimeMillis();
        Member member = memberService.findOne(user.getId());
        log.info("Querying time taken = {}",System.currentTimeMillis()-current);
        if (member == null){
            return  MessageResult.error("Not logged in");
        }
        String secret = GoogleAuthenticatorUtil.generateSecretKey();
        log.info("Secret completed time-consuming = {}",System.currentTimeMillis()-current);
        String qrBarcodeURL = GoogleAuthenticatorUtil.getQRBarcodeURL(member.getId().toString(),
                googleHost, secret);
        log.info("qrBarcodeURL completed Time-consuming = {}",System.currentTimeMillis()-current);
        ObjectNode jsonObject = objectMapper.createObjectNode();
        jsonObject.put("link", qrBarcodeURL);
        jsonObject.put("secret", secret);
        log.info("jsonObject completed time-consuming ={}",System.currentTimeMillis()-current);
        MessageResult messageResult = new MessageResult();
        messageResult.setData(jsonObject);
        messageResult.setMessage("Get successful");
        log.info("Execution is completed Time to take ={}",System.currentTimeMillis()-current);
        return  messageResult;
    }

    @Override
    public MessageResult jcgoogle(String codes, AuthMember user, String password) {
        // enter the code shown on device. Edit this and run it fast before the
        // code expires!
        // String GoogleKey = (String) request.getSession().getAttribute("googleKey");
        Member member = memberService.findOne(user.getId());
        String GoogleKey = member.getGoogleKey();
        if(StringUtils.isEmpty(password)){
            return MessageResult.error("Password cannot be empty");
        }
        try {
            if(!(Md5.md5Digest(password + member.getSalt()).toLowerCase().equals(member.getPassword().toLowerCase()))){
                return MessageResult.error("Error password");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        long code = Long.parseLong(codes);
        long t = System.currentTimeMillis();
        GoogleAuthenticatorUtil ga = new GoogleAuthenticatorUtil();
        // ga.setWindowSize(0); // should give 5 * 30 seconds of grace...
        boolean r = ga.checkCode(GoogleKey, code, t);
        if(!r){
            return MessageResult.error("Validation failed");

        }else{
            member.setGoogleDate(new Date());
            member.setGoogleState(0);
            Member result = memberService.save(member);
            if(result != null){
                return MessageResult.success("Unbinding succeeded");
            }else{
                return MessageResult.error("Unbinding failed");
            }
        }
    }

    @Override
    public MessageResult googleAuth(String codes, AuthMember user, String secret) {
        Member member = memberService.findOne(user.getId());
        long code = Long.parseLong(codes);
        long t = System.currentTimeMillis();
        GoogleAuthenticatorUtil ga = new GoogleAuthenticatorUtil();
        boolean r = ga.checkCode(secret, code, t);
        if(!r){
            return MessageResult.error("Validation failed");
        }else{
            member.setGoogleState(1);
            member.setGoogleKey(secret);
            member.setGoogleDate(new Date());
            Member result = memberService.save(member);
            if(result != null){
                return MessageResult.success("Binding succeeded");
            }else{
                return MessageResult.error("Binding failed");
            }
        }
    }
}
