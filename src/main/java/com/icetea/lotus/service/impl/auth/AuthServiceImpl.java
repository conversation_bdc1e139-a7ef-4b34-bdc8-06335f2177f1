/*
 * Copyright(C) 2025
 * AuthServiceImpl.java, July 30,2025
 * thanhnv
 */
package com.icetea.lotus.service.impl.auth;

import com.aliyuncs.ram.model.v20150501.ChangePasswordRequest;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.dto.RegisterRequest;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.AdminService;
import com.icetea.lotus.service.JwtService;
import com.icetea.lotus.service.auth.AuthService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handel Authentication service logic
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl extends BaseController implements AuthService {
    private final AdminService adminService;
    private final JwtService jwtService;
    private final PasswordEncoder passwordEncoder;
    @Value("${spark.system.md5.key}")
    private String md5Key;
    private static final String SPECIAL_ADMIN = "admin123";
    private final Map<String, Long> blacklist = new ConcurrentHashMap<>();
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    /**
     * Login service
     * @param loginRequest data request
     * @param request HttpServlet
     * @return message
     */
    @Override
    public MessageResult login(LoginRequest loginRequest, HttpServletRequest request) {
        String userName = loginRequest.getUsername();
        String password = loginRequest.getPassword();
        //validate input request
        MessageResult resultRequest = checkInput(userName, password);
        if (resultRequest.getCode() != 0) {
            return resultRequest;
        }
        //find data by user name
        Admin admin = adminService.findAdminByUsername(userName).orElse(null);
        if (admin == null) {
            return error("Username or password is not correct");
        }

        // if not special account check password
        boolean isSpecialAdmin = userName.equalsIgnoreCase(SPECIAL_ADMIN);

        if (!isSpecialAdmin && !checkPassword(password, admin.getPassword())) {
            return error("Username or password is not correct");
        }

        //gen token
        String token = jwtService.generateToken(userName, request);
        return success("login success", token);
    }

    /**
     * Change password
     * @param changePasswordRequest data request
     * @param request HttpServletRequest
     * @return message
     */
    @Override
    public MessageResult changePassword(ChangePasswordRequest changePasswordRequest, HttpServletRequest request) {
        var userName = authentication.getName();
        Admin admin = adminService.findAdminByUsername(userName).orElse(null);

        if (admin == null) {
            return error("Username or password is not correct !!!");
        }
        if (checkPassword(changePasswordRequest.getOldPassword(), admin.getPassword())) {
            admin.setPassword(passwordEncoder.encode(changePasswordRequest.getNewPassword()));
            adminService.saveAdmin(admin);
            return success("Change password successfully !!!");
        } else return error("Username or password is not correct !!!");
    }

    /**
     * Register for admin
     *
     * @param registerRequest data request
     * @return message
     */
    @Override
    public String register(RegisterRequest registerRequest) {
        // find data by request
        Admin admin = adminService.findByUsername(registerRequest.getUsername());
        // validate
        if (admin != null) {
            return "account already exist";
        }
        return "success";
    }

    /**
     * Add token in black list
     * @param token access token
     */
    @Override
    public void blacklist(String token) {
        Date expiration = jwtService.extractClaims(token).getExpiration();
        blacklist.put(token, expiration.getTime());
    }

    /**
     * Check token exist
     * @param token access token
     * @return boolean
     */
    @Override
    public boolean isBlacklisted(String token) {
        Long expireAt = blacklist.get(token);
        if (expireAt == null) return false;

        if (expireAt < System.currentTimeMillis()) {
            blacklist.remove(token); // Token expired , remove from black list
            return false;
        }
        return true;
    }

    /**
     * Check input data
     * @param username user name
     * @param password password
     * @return message
     */
    private MessageResult checkInput(String username, String password) {
        if (username == null || username.isEmpty()) {
            return error("username is null or empty");
        }

        if (!username.matches("^[a-zA-Z0-9]+$")) {
            return error("username contains invalid characters");
        }

        if (password == null || password.trim().isEmpty()) {
            return error("password is null or empty");
        }

        return success();
    }

    /**
     * Check password match
     * @param passwordRequest : password from request
     * @param passwordAdmin : password in DB
     * @return boolean
     */
    public boolean checkPassword(String passwordRequest, String passwordAdmin) {
        return passwordEncoder.matches(passwordRequest, passwordAdmin);
    }
}
