package com.icetea.lotus.service.impl.cms;

import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysAdvertiseLocation;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.QSysAdvertise;
import com.icetea.lotus.entity.spot.SysAdvertise;
import com.icetea.lotus.model.screen.SysAdvertiseScreen;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.SysAdvertiseService;
import com.icetea.lotus.service.cms.AdvertiseService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.icetea.lotus.util.UUIDUtil;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.icetea.lotus.entity.spot.QSysAdvertise.sysAdvertise;
import static org.springframework.util.Assert.notNull;

/**
 * The type Advertise service.
 */
@Service
@RequiredArgsConstructor
public class AdvertiseServiceImpl extends BaseController implements AdvertiseService {

    private final SysAdvertiseService sysAdvertiseService;

    private final LocaleMessageSourceService msService;

    @Override
    public MessageResult findOne(SysAdvertise sysAdvertise, BindingResult bindingResult) {
        Date end = DateUtil.strToDate(sysAdvertise.getEndTime());
        Date start = DateUtil.strToDate(sysAdvertise.getStartTime());
        Assert.isTrue(end.after(start), msService.getMessage("START_END_TIME"));
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        sysAdvertise.setSerialNumber(UUIDUtil.getUUID());
        sysAdvertise.setCreateTime(DateUtil.getCurrentDate());

        updateSort(sysAdvertise.getSort(), sysAdvertise.getSysAdvertiseLocation().getOrdinal());
        return success(sysAdvertiseService.save(sysAdvertise));
    }

    @Override
    public MessageResult all() {
        List<SysAdvertise> all = sysAdvertiseService.findAll();
        if (all != null & all.size() > 0) {
            return success(all);
        }
        return error("data null");
    }

    @Override
    public MessageResult findOne(String serialNumber) {
        SysAdvertise sysAdvertise = sysAdvertiseService.findOne(serialNumber);
        notNull(sysAdvertise, "validate serialNumber!");
        return success(sysAdvertise);
    }

    @Override
    public MessageResult update(SysAdvertise sysAdvertise, BindingResult bindingResult) {
        notNull(sysAdvertise.getSerialNumber(), "validate serialNumber(null)!");
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        SysAdvertise one = sysAdvertiseService.findOne(sysAdvertise.getSerialNumber());
        notNull(one, "validate serialNumber!");
        updateSort(sysAdvertise.getSort(), sysAdvertise.getSysAdvertiseLocation().getOrdinal());
        sysAdvertiseService.save(sysAdvertise);
        return success();
    }

    @Override
    public MessageResult delete(String[] ids) {
        sysAdvertiseService.deleteBatch(ids);
        return success();
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, SysAdvertiseScreen screen) {
        Predicate predicate = getPredicate(screen);
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<SysAdvertise> all = sysAdvertiseService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }


    @Override
    public MessageResult toTop(String serialNum) {
        SysAdvertise advertise = sysAdvertiseService.findOne(serialNum);
        int a = sysAdvertiseService.getMaxSort();
        advertise.setSort(a + 1);
        sysAdvertiseService.save(advertise);
        return success();
    }

    @Override
    public MessageResult outExcel(String serialNumber, SysAdvertiseLocation sysAdvertiseLocation, CommonStatus status, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Predicate> predicateList = getPredicateList(serialNumber, sysAdvertiseLocation, status);
        List<SysAdvertise> list = sysAdvertiseService.query(predicateList, null, null).getContent();
        return new FileUtil<SysAdvertise>(new LocaleMessageSourceService()).exportExcel(request, response, list, "sysAdvertise");
    }


    private List<Predicate> getPredicateList(String serialNumber, SysAdvertiseLocation sysAdvertiseLocation, CommonStatus status) {
        ArrayList<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(serialNumber)) {
            predicates.add(sysAdvertise.serialNumber.eq(serialNumber));
        }
        if (sysAdvertiseLocation != null) {
            predicates.add(sysAdvertise.sysAdvertiseLocation.eq(sysAdvertiseLocation));
        }
        if (status != null) {
            predicates.add(sysAdvertise.status.eq(status));
        }
        return predicates;
    }


    /**
     * Update sort.
     *
     * @param sort the sort
     * @param cate the cate
     */
    /* ***
     * Advertising sorting
     * Get all advertisements, without the serial number setting, when the serial number exists, all increments greater than the serial number
     * @param sort
     */
    public void updateSort(int sort, int cate) {
        List<SysAdvertise> list = sysAdvertiseService.querySysAdvertise(sort, cate);
        // Filter ads larger than this serial number
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSort(list.get(i).getSort() + 1);
        }
    }

    private Predicate getPredicate(SysAdvertiseScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (screen.getStatus() != null) {
            booleanExpressions.add(QSysAdvertise.sysAdvertise.status.eq(screen.getStatus()));
        }
        if (screen.getSysAdvertiseLocation() != null) {
            booleanExpressions.add(QSysAdvertise.sysAdvertise.sysAdvertiseLocation.eq(screen.getSysAdvertiseLocation()));
        }
        if (StringUtils.isNotBlank(screen.getSerialNumber())) {
            booleanExpressions.add(QSysAdvertise.sysAdvertise.serialNumber.like("%" + screen.getSerialNumber() + "%"));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }


}
