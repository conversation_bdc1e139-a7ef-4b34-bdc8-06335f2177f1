package com.icetea.lotus.service.impl.exchange;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.entity.spot.InitPlate;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.InitPlateService;
import com.icetea.lotus.service.exchange.HTLExchangeInitPlateService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * The type Htl exchange init plate service.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HTLExchangeInitPlateServiceImpl implements HTLExchangeInitPlateService {
    private final ExchangeCoinService exchangeCoinService;
    private final InitPlateService initPlateService;
    private final RedisTemplate redisTemplate;

    @Override
    public MessageResult queryExchangeInitPlate() throws Exception {
        MessageResult mr =new MessageResult();
        try {

            InitPlate initPlate = initPlateService.findInitPlateBySymbol("HTL/ETH");
            mr.setCode(0);
            mr.setMessage("success");
            mr.setData(initPlate);
        }catch (Exception e){
            log.info(">>>>queryExchanegCoin Error",e);
            e.printStackTrace();
            throw new Exception(e);
        }
        return mr;
    }

    @Override
    public MessageResult queryDetailExchangeInitPlate(long id) throws Exception {
        MessageResult mr = new MessageResult();
        try {
            mr.setData(initPlateService.findByInitPlateId(id));
            mr.setCode(0);
            mr.setMessage("success");
        }catch (Exception e){
            log.info(">>>>queryDetailExchangeInitPlate Error={}",e);
            e.printStackTrace();
            throw new Exception(e);
        }

        return mr;
    }

    @Override
    public MessageResult deleteExchangeInitPlate(long id) throws Exception {
        MessageResult mr = new MessageResult();
        try {
            InitPlate initPlate = initPlateService.findByInitPlateId(id);
            if(initPlate==null){
                mr.setCode(500);
                mr.setMessage("The record does not exist");
                return mr;
            }
            initPlateService.delete(id);
            ValueOperations valueOperations = redisTemplate.opsForValue();
            String key = SysConstant.EXCHANGE_INIT_PLATE_SYMBOL_KEY+initPlate.getSymbol();
            valueOperations.getOperations().delete(key);
            mr.setCode(0);
            mr.setMessage("success");
        }catch (Exception e){
            log.info(">>>>deleteExchangeInitPlate Error={}",e);
            e.printStackTrace();
            throw new Exception(e);
        }

        return  mr ;
    }

    @Override
    public MessageResult updateExchangeInitPlate(InitPlate initPlate) throws Exception {
        MessageResult mr = new MessageResult();
        try {
            if (checkInitPlateParams(initPlate, mr)){
                return mr;
            }
            if(initPlate.getId()==null){
                mr.setCode(500);
                mr.setMessage("The record does not exist");
                return mr;
            }
            if(initPlateService.findByInitPlateId(initPlate.getId())==null){
                mr.setCode(500);
                mr.setMessage("The record does not exist");
                return mr;
            }
            initPlateService.saveAndFlush(initPlate);
            ValueOperations valueOperations = redisTemplate.opsForValue();
            String key = SysConstant.EXCHANGE_INIT_PLATE_SYMBOL_KEY+initPlate.getSymbol();
            valueOperations.getOperations().delete(key);
            mr.setCode(0);
            mr.setMessage("Modification was successful");
        }catch (Exception e){
            log.info(".>>>updateInitPlate Error ={}",e);
            e.printStackTrace();
            throw new Exception(e);
        }

        return mr;
    }


    /**
     * Check init plate params boolean.
     *
     * @param initPlate the init plate
     * @param mr        the mr
     * @return the boolean
     */
    public boolean checkInitPlateParams(@RequestParam InitPlate initPlate, MessageResult mr) {
        if(StringUtils.isEmpty(initPlate.getRelativeTime())||
                StringUtils.isEmpty(initPlate.getInitPrice())||
                StringUtils.isEmpty(initPlate.getFinalPrice())||
                StringUtils.isEmpty(initPlate.getRelativeTime())||
                StringUtils.isEmpty(initPlate.getSymbol())){
            mr.setCode(500);
            mr.setMessage("The parameters are illegal, please verify the parameters");
            return true;
        }
        // Verify parameters
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(initPlate.getSymbol());
        if(exchangeCoin==null || exchangeCoin.getEnable()==2){
            mr.setCode(500);
            mr.setMessage("The transaction pair is illegal, please verify");
            return true;
        }
        int interferenceFactor =Integer.parseInt(initPlate.getInterferenceFactor());
        if(interferenceFactor<0 || interferenceFactor>70){
            mr.setCode(500);
            mr.setMessage("The interference factor is illegal, please enter an integer between 1-70.");
            return true;
        }
        double initPrice = Double.parseDouble(initPlate.getInitPrice());
        double finalPrice =Double.parseDouble(initPlate.getFinalPrice());
        if(initPrice==finalPrice){
            mr.setCode(500);
            mr.setMessage("The price difference is 0, please read the adjustment");
            return true;
        }
        return false;
    }
}
