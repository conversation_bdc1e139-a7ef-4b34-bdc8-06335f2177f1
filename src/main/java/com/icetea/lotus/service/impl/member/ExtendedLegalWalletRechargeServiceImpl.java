package com.icetea.lotus.service.impl.member;

import com.icetea.lotus.constant.LegalWalletState;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.LegalWalletRecharge;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QLegalWalletRecharge;
import com.icetea.lotus.model.screen.LegalWalletRechargeScreen;
import com.icetea.lotus.service.LegalWalletRechargeService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedLegalWalletRechargeService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.mysema.commons.lang.Assert;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * The type Extended legal wallet recharge service.
 */
@Service
public class ExtendedLegalWalletRechargeServiceImpl extends BaseAdminController implements ExtendedLegalWalletRechargeService {

    private final LegalWalletRechargeService legalWalletRechargeService;

    private final MemberWalletService walletService;

    public ExtendedLegalWalletRechargeServiceImpl(BaseAdminService baseAdminService, LegalWalletRechargeService legalWalletRechargeService, MemberWalletService walletService) {
        super(baseAdminService);
        this.legalWalletRechargeService = legalWalletRechargeService;
        this.walletService = walletService;
    }

    @Override
    public MessageResult page(PageModel pageModel, LegalWalletRechargeScreen screen) {
        Predicate predicate = getPredicate(screen);
        Page<LegalWalletRecharge> page = legalWalletRechargeService.findAll(predicate, pageModel);
        return success(page);
    }

    @Override
    public MessageResult id(Long id) {
        LegalWalletRecharge legalWalletRecharge = legalWalletRechargeService.findOne(id);
        Assert.notNull(legalWalletRecharge, "validate id!");
        return success(legalWalletRecharge);
    }

    @Override
    public MessageResult pass(Long id) {
        // Recharge verification
        LegalWalletRecharge legalWalletRecharge = legalWalletRechargeService.findOne(id);
        Assert.notNull(legalWalletRecharge, "validate id!");
        Assert.isTrue(legalWalletRecharge.getState() == LegalWalletState.APPLYING, "The application has been completed!");
        // Verify wallet
        MemberWallet wallet = walletService.findByCoinAndMember(legalWalletRecharge.getCoin(), legalWalletRecharge.getMember());
        org.springframework.util.Assert.notNull(wallet, "wallet null!");
        // Recharge request through business
        legalWalletRechargeService.pass(wallet, legalWalletRecharge);
        return success();
    }

    @Override
    public MessageResult noPass(Long id) {
        LegalWalletRecharge legalWalletRecharge = legalWalletRechargeService.findOne(id);
        Assert.notNull(legalWalletRecharge, "validate id!");
        Assert.isTrue(legalWalletRecharge.getState() == LegalWalletState.APPLYING, "The application has been completed!");
        legalWalletRechargeService.noPass(legalWalletRecharge);
        return success();
    }

    // condition
    private Predicate getPredicate(LegalWalletRechargeScreen screen) {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(screen.getUsername())) {
            booleanExpressions.add(QLegalWalletRecharge.legalWalletRecharge.member.username.eq(screen.getUsername()));
        }
        if (screen.getStatus() != null) {
            booleanExpressions.add(QLegalWalletRecharge.legalWalletRecharge.state.eq(screen.getStatus()));
        }
        if (StringUtils.isNotBlank(screen.getCoinName())) {
            booleanExpressions.add(QLegalWalletRecharge.legalWalletRecharge.coin.name.eq(screen.getCoinName()));
        }
        return PredicateUtils.getPredicate(booleanExpressions);
    }

}
