package com.icetea.lotus.service.code;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.util.MessageResult;
import org.springframework.web.bind.annotation.SessionAttribute;

public interface SmsProviderService {
    MessageResult sendReviseCode(Admin admin);
    MessageResult sendExchangeCoinSet(Admin admin);
    MessageResult sendTransfer(Admin admin);
    MessageResult send(String phone);
}
