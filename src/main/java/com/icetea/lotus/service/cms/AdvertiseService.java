package com.icetea.lotus.service.cms;

import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysAdvertiseLocation;
import com.icetea.lotus.entity.spot.SysAdvertise;
import com.icetea.lotus.model.screen.SysAdvertiseScreen;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.Predicate;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

public interface AdvertiseService {
    MessageResult findOne(SysAdvertise sysAdvertise, BindingResult bindingResult);

    MessageResult all();

    MessageResult findOne(String serialNumber);

    MessageResult update(SysAdvertise sysAdvertise, BindingResult bindingResult);

    MessageResult delete(String[] ids);

    MessageResult pageQuery(PageModel pageModel, SysAdvertiseScreen screen);

    MessageResult toTop(String serialNum);

    MessageResult outExcel(
            String serialNumber,
            SysAdvertiseLocation sysAdvertiseLocation,
            CommonStatus status,
            HttpServletRequest request, HttpServletResponse response) throws Exception;
}
