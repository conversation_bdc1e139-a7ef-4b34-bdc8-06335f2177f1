package com.icetea.lotus.service.cms;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.SysHelp;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

public interface HelpService {
    MessageResult create(SysHelp sysHelp, BindingResult bindingResult);

    MessageResult all();

    MessageResult toTop(long id);

    MessageResult toDown(long id);

    MessageResult detail(Long id);

    MessageResult update(SysHelp sysHelp, BindingResult bindingResult);

    MessageResult deleteOne(Long[] ids);

    MessageResult pageQuery(PageModel pageModel);

    MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception;
}
