package com.icetea.lotus.service.exchange;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.model.exchange.ExchangeCoinRateRequest;
import com.icetea.lotus.model.exchange.RobotConfigRequest;
import com.icetea.lotus.model.screen.ExchangeCoinScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;

public interface ExtendedExchangeCoinService {
    MessageResult exchangeCoinList(ExchangeCoin exchangeCoin);

    MessageResult exchangeCoinList(PageModel pageModel, ExchangeCoinScreen screen);

    MessageResult detail(String symbol);

    MessageResult deletes(String[] ids);

    MessageResult alterExchangeCoinRate(ExchangeCoinRateRequest exchangeCoinRateRequest, Admin admin);

    MessageResult startExchangeCoinEngine(String symbol, String password, Admin admin);

    MessageResult stopExchangeCoinEngine(String symbol, String password, Admin admin);

    MessageResult resetExchangeCoinEngine(String symbol, String password, Admin admin);

    MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception;

    MessageResult getAllBaseSymbolUnits();

    MessageResult getAllCoinSymbolUnits(String baseSymbol);

    MessageResult cancelAllOrderBySymbol(String symbol, String password, Admin admin);

    MessageResult overviewExchangeCoin(String symbol);

    MessageResult getRobotConfig(String symbol);

    MessageResult alterRobotConfig(RobotConfigRequest robotConfigRequest, Admin admin);

    MessageResult createRobotConfig(RobotConfigRequest robotConfigRequest, Admin admin);

    MessageResult createRobotKlineData(String symbol, String kdate, String kline, Integer pricePencent);

    MessageResult createRobotFlow(String symbol, String pair, BigDecimal flowPercent);

    MessageResult customRobotCoinList();

    MessageResult robotKlineDataList(String symbol, String kdate);
}
