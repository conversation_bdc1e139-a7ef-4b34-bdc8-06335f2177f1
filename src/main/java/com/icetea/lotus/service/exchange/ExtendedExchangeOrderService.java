package com.icetea.lotus.service.exchange;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.model.screen.ExchangeOrderScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public interface ExtendedExchangeOrderService {
    MessageResult all();

    MessageResult detail(String id);

    MessageResult page(PageModel pageModel, ExchangeOrderScreen screen, HttpServletResponse response) throws IOException;

    MessageResult outExcel(Long memberId, ExchangeOrderType type, String symbol, ExchangeOrderStatus status, ExchangeOrderDirection direction, HttpServletRequest request, HttpServletResponse response) throws Exception;

    MessageResult cancelOrder(String orderId);

    MessageResult getOrderDetails(PageModel pageModel, Long memberId);

    MessageResult findAllSymbol();

    MessageResult getExchangeOrderMineListByEs(Long memberId, String phone, String exchangeCoin, String startTime, String endTime, int page, int pageSize);
}
