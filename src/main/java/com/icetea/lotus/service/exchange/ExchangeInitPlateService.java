package com.icetea.lotus.service.exchange;

import com.icetea.lotus.entity.spot.InitPlate;
import com.icetea.lotus.util.MessageResult;

public interface ExchangeInitPlateService {
    MessageResult queryExchangeInitPlate(int pageNo, int pageSize, String symbol) throws Exception;

    MessageResult queryDetailExchangeInitPlate(long id) throws Exception;

    MessageResult deleteExchangeInitPlate(long id) throws Exception;

    MessageResult updateExchangeInitPlate(InitPlate initPlate) throws Exception;

    MessageResult addExchangeInitPlate(InitPlate initPlate) throws Exception;
}
