package com.icetea.lotus.service.promotion;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.PromotionRewardType;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.RewardPromotionSetting;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedRewardPromotionService {
     MessageResult merge(RewardPromotionSetting setting, Admin admin, String unit, String password) ;

     MessageResult detail(Long id);

     MessageResult pageQuery(PageModel pageModel, BooleanEnum enable, PromotionRewardType type);

     MessageResult deletes(long[] ids);
}
