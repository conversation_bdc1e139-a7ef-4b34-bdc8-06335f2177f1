package com.icetea.lotus.service.promotion;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.MemberPromotionScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Map;

public interface ExtendedMemberPromotionService {
     MessageResult page(PageModel pageModel, MemberPromotionScreen screen);

     MessageResult promotionDetails(PageModel pageModel, Long memberId);

     void outExcel(PageModel pageModel, MemberPromotionScreen screen, HttpServletResponse response) throws IOException;

}
