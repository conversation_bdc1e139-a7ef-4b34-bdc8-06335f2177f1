package com.icetea.lotus.service.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.create.SignCreate;
import com.icetea.lotus.model.screen.SignScreen;
import com.icetea.lotus.model.update.SignUpdate;
import com.icetea.lotus.util.MessageResult;
import org.springframework.validation.BindingResult;

/**
 * The interface Signed service.
 */
public interface SignedService {
    /**
     * Create message result.
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @return the message result
     */
    MessageResult create(SignCreate model, BindingResult bindingResult);

    /**
     * Update message result.
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @param id            the id
     * @return the message result
     */
    MessageResult update(SignUpdate model, BindingResult bindingResult, Long id);

    /**
     * Page query message result.
     *
     * @param screen    the screen
     * @param pageModel the page model
     * @return the message result
     */
    MessageResult pageQuery(SignScreen screen, PageModel pageModel);

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    MessageResult detail(Long id);

    /**
     * Early closing message result.
     *
     * @param id the id
     * @return the message result
     */
    MessageResult earlyClosing(Long id);

    /**
     * Is message result.
     *
     * @return the message result
     */
    MessageResult is();
}
