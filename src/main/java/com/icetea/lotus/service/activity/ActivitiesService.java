package com.icetea.lotus.service.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Activity;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.model.ActivityRequest;
import com.icetea.lotus.util.MessageResult;
import java.math.BigDecimal;

/**
 * The interface Activity service.
 */
public interface ActivitiesService {
    /**
     * Activity list message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    MessageResult activityList(PageModel pageModel ,String unit);

    /**
     * Locked activity list message result.
     *
     * @param type the type
     * @param step the step
     * @return the message result
     */
    MessageResult lockedActivityList(Integer type , Integer step);

    /**
     * Exchange coin list message result.
     *
     * @param activity the activity
     * @return the message result
     */
    MessageResult ExchangeCoinList(Activity activity);

    /**
     * Alter activity message result.
     *
     * @param id       the id
     * @param progress the progress
     * @return the message result
     */
    MessageResult alterActivity(Long id, Integer progress);

    /**
     * Alter activity freeze amount message result.
     *
     * @param id           the id
     * @param freezeAmount the freeze amount
     * @return the message result
     */
    MessageResult alterActivityFreezeAmount(Long id, BigDecimal freezeAmount);

    /**
     * Alter activity traded amount message result.
     *
     * @param id           the id
     * @param tradedAmount the traded amount
     * @return the message result
     */
    MessageResult alterActivityTradedAmount(Long id, BigDecimal tradedAmount);

    /**
     * Alter activity message result.
     *
     * @param activityRequest the activity request
     * @param admin           the admin
     * @return the message result
     */
    MessageResult alterActivity(ActivityRequest activityRequest, Admin admin);

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    MessageResult detail(Long id);

    /**
     * Order list message result.
     *
     * @param aid the aid
     * @return the message result
     */
    MessageResult orderList(Long aid);

    /**
     * Distribute message result.
     *
     * @param oid the oid
     * @return the message result
     */
    MessageResult distribute(Long oid);

    /**
     * Lock member coin message result.
     *
     * @param memberId   the member id
     * @param activityId the activity id
     * @param unit       the unit
     * @param amount     the amount
     * @return the message result
     */
    MessageResult lockMemberCoin(Long memberId, Long activityId, String unit, BigDecimal amount);
}
