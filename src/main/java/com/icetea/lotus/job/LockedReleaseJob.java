package com.icetea.lotus.job;

import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.entity.spot.LockedOrder;
import com.icetea.lotus.entity.spot.LockedOrderDetail;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.service.LockedOrderDetailService;
import com.icetea.lotus.service.LockedOrderService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class LockedReleaseJob {


    private final LockedOrderDetailService lockedOrderDetailService;
    private final LockedOrderService lockedOrderService;
    private final MemberWalletService memberWalletService;
    private final MemberTransactionService memberTransactionService;
    private final MemberService memberService;

    private void increaseToRelease() {

    }

    /**
     * Daily processing: Release locking at 11:30 p.m. every day
     */
    @Scheduled(cron = "0 30 23 * * *")
    // @Transactional(rollbackFor = Exception.class)
    public void release() {
        List<LockedOrder> list = lockedOrderService.findAllByLockedStatus(1);

        Date currentDate = DateUtil.getCurrentDate();
        for (LockedOrder item : list) {
            if (this.checkNuccessary(item)) {
                this.doRelease(item);
            }
        }
    }

    private boolean checkNuccessary(LockedOrder item) {
        if (item.getLockedStatus() != 1) { // Not a release state
            return false;
        }
        // Date cycle, direct release
        // Weekly period, calculate whether it is a release period
        long days = DateUtil.diffDays(item.getCreateTime(), DateUtil.getCurrentDate());
        if (days == 0) {
            return false;
        }
        if (item.getPeriod() == 1) {
            if (days % 7 != 0) return false;
        }
        // Monthly cycle, calculate whether it is a release cycle (release once every 30 days)
        if (item.getPeriod() == 2) {
            if (days % 30 != 0) return false;
        }
        // Annual cycle, calculate whether it is a release cycle (release once every 365 days)
        if (item.getPeriod() == 3) {
            if (days % 365 != 0) return false;
        }
        return true;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void doRelease(LockedOrder item) {

        Member member = memberService.findOne(item.getMemberId());
        // Get the current wallet
        MemberWallet userWallet = memberWalletService.findByCoinUnitAndMemberId(item.getReleaseUnit(), item.getMemberId());

        LockedOrderDetail lod = new LockedOrderDetail();
        BigDecimal rAmount = item.getCurrentReleaseamount(); // Release equal amount
        if (item.getReleaseType() == 1) { // Release in a timely manner
            rAmount = item.getTotalLocked().subtract(item.getTotalRelease()).multiply(item.getReleaseCurrentpercent()); // Remaining locked positions x release ratio
        }
        if (item.getLockedDays() - item.getReleasedDays() == 1) { // The last issue is released
            rAmount = item.getTotalLocked().subtract(item.getTotalRelease()); // Release what was not released in the end
        }
        // Save Release Details
        lod.setOutput(rAmount);
        lod.setReleaseUnit(item.getReleaseUnit());
        lod.setMemberId(item.getMemberId());
        lod.setLockedOrderId(item.getId());
        lod.setCreateTime(DateUtil.getCurrentDate());
        lockedOrderDetailService.save(lod);

        // Deduct assets from user wallet table
        if (userWallet == null) {
            log.info("=======>userWallet is null");
        }
        if (rAmount == null) {
            log.info("=======>rAmount is null");
        }
        memberWalletService.decreaseToRelease(userWallet.getId(), rAmount);
        // Increase balance assets
        memberWalletService.increaseBalance(userWallet.getId(), rAmount);
        // Increase asset change record
        MemberTransaction memberTransaction1 = new MemberTransaction();
        memberTransaction1.setFee(BigDecimal.ZERO);
        memberTransaction1.setAmount(rAmount);
        memberTransaction1.setMemberId(item.getMemberId());
        memberTransaction1.setSymbol(item.getReleaseUnit());
        memberTransaction1.setType(TransactionType.ACTIVITY_BUY);
        memberTransaction1.setCreateTime(DateUtil.getCurrentDate());
        memberTransaction1.setRealFee("0");
        memberTransaction1.setDiscountFee("0");
        memberTransactionService.save(memberTransaction1);
        // Update the main table
        item.setTotalRelease(item.getTotalRelease().add(rAmount));
        item.setReleasedDays(item.getReleasedDays() + 1);
        if (item.getLockedDays() - item.getReleasedDays() == 0) { // The last issue
            item.setLockedStatus(2); // Ended
        }
        lockedOrderService.save(item);
    }
}
