package com.icetea.lotus.job;

import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.RedEnvelope;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.RedEnvelopeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;

@Component
@Slf4j
public class CheckRedEnvelopeJob {
	private final RedEnvelopeService redEnvelopeService;
    private final MemberWalletService memberWalletService;
    private final MemberTransactionService transactionService;

	public CheckRedEnvelopeJob(RedEnvelopeService redEnvelopeService,
							   MemberWalletService memberWalletService,
							   MemberTransactionService transactionService) {
		this.redEnvelopeService = redEnvelopeService;
		this.memberWalletService = memberWalletService;
		this.transactionService = transactionService;
	}
	/**
	 * Check for expired red envelopes (execute once every 5 minutes)
	 * After the red envelopes issued by the platform expire, set the status without returning assets
	 * After the red envelope issued by the user expires, set the status and return the assets that the user has not received.
	 */
    @Scheduled(cron = "0 */5 * * * *")
    public void checkExpired(){
    	List<RedEnvelope> list = redEnvelopeService.findAllByState(0);
    	for(int i = 0; i < list.size(); i++) {
    		RedEnvelope redEnvelope = list.get(i);
    		long currentTime = Calendar.getInstance().getTimeInMillis(); // Current timestamp
    		// Timeout order
	    	if(currentTime >= (redEnvelope.getCreateTime().getTime() + redEnvelope.getExpiredHours() * 60 * 60 * 1000)) {
				redEnvelope.setState((redEnvelope.getReceiveCount() < redEnvelope.getCount()) ? 2 : 1); // Expired and not finished
	    		redEnvelopeService.saveAndFlush(redEnvelope);
	    		processTransaction(redEnvelope);
	    	}else {
	    		// Get it in advance
	    		if(redEnvelope.getReceiveCount() == redEnvelope.getCount()) {
	    			redEnvelope.setState(1); // Completed normally
	    			redEnvelopeService.saveAndFlush(redEnvelope);
					processTransaction(redEnvelope);
	    		}
	    	}
    	}
    }

	private void processTransaction(RedEnvelope redEnvelope){
		if(redEnvelope.getPlateform() == 0) {
			MemberWallet wallet = memberWalletService.findByCoinUnitAndMemberId(redEnvelope.getUnit(), redEnvelope.getMemberId());
			BigDecimal refundAmount = redEnvelope.getTotalAmount().subtract(redEnvelope.getReceiveAmount());
			memberWalletService.decreaseFrozen(wallet.getId(), redEnvelope.getReceiveAmount());
			// Increase funding records
			MemberTransaction transaction2 = new MemberTransaction();
			transaction2.setAmount(redEnvelope.getReceiveAmount());
			transaction2.setSymbol(redEnvelope.getUnit());
			transaction2.setAddress("");
			transaction2.setMemberId(wallet.getMemberId());
			transaction2.setType(TransactionType.EXCHANGE);
			transaction2.setFee(BigDecimal.ZERO);
			transaction2.setRealFee("0");
			transaction2.setDiscountFee("0");
			transactionService.save(transaction2);

			if(refundAmount.compareTo(BigDecimal.ZERO) > 0) {
				memberWalletService.thawBalance(wallet, refundAmount);
			}
		}
	}
}
