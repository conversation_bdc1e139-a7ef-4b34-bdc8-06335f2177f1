package com.icetea.lotus.job;

import com.icetea.lotus.constant.TransactionTypeEnum;
import com.icetea.lotus.dao.ExchangeOrderRepository;
import com.icetea.lotus.dao.MemberDao;
import com.icetea.lotus.dao.MemberDepositDao;
import com.icetea.lotus.dao.MemberLogDao;
import com.icetea.lotus.dao.WithdrawRecordDao;
import com.icetea.lotus.entity.spot.ExchangeTurnoverStatistics;
import com.icetea.lotus.entity.spot.MemberLog;
import com.icetea.lotus.entity.spot.TurnoverStatistics;
import com.icetea.lotus.service.OrderService;
import com.icetea.lotus.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class MemberStatisticsJob {

    private final MemberDao memberDao;

    private final OrderService orderService;

    private final ExchangeOrderRepository exchangeOrderRepository;

    private final MemberDepositDao memberDepositDao;

    private final MongoTemplate mongoTemplate;

    private final WithdrawRecordDao withdrawRecordDao;

    private final MemberLogDao memberLogDao;

    /**
     * Member registration/real name/certified merchant statistics
     */
    @Scheduled(cron = "0 34 3 * * ?")
    public void statisticsMember() {
        try {
            if (!mongoTemplate.collectionExists("member_log")) {
                List<Date> list = getDateList();
                String dateStr = "";
                for (Date date : list) {
                    dateStr = DateUtil.YYYY_MM_DD.format(date);
                    statisticsMember(dateStr, date);
                }
            } else {
                Date date = DateUtil.dateAddDay(DateUtil.getCurrentDate(), -1);
                String dateStr = DateUtil.getFormatTime(DateUtil.YYYY_MM_DD, date);
                statisticsMember(dateStr, date);
            }
        } catch (ParseException e) {
            log.error("Date parsing exception", e);
        }

    }

    /**
     * Franchise currency/coin recharge/coin withdrawal processing fee
     * Coin transaction fee statistics
     * Franchise currency trading volume/transaction volume Statistics
     */
    @Scheduled(cron = "0 24 3 * * ?")
    public void turnoverStatistics() {
        try {
            if (!mongoTemplate.collectionExists("turnover_statistics")) {
                List<Date> list = getDateList();
                String dateStr = "";
                for (Date date : list) {
                    dateStr = DateUtil.YYYY_MM_DD.format(date);
                    statisticsFee(dateStr, date);
                }
            } else {
                Date date = DateUtil.dateAddDay(DateUtil.getCurrentDate(), -1);
                String dateStr = DateUtil.getFormatTime(DateUtil.YYYY_MM_DD, date);
                statisticsFee(dateStr, date);
            }

        } catch (ParseException e) {
            log.error("Date parsing exception", e);
        }

    }

    /**
     * Coin transaction volume/transaction volume Statistics
     */
    @Scheduled(cron = "0 14 3 * * ?")
    public void exchangeStatistics() {
        try {
            if (!mongoTemplate.collectionExists("exchange_turnover_statistics")) {
                List<Date> list = getDateList();
                String dateStr = "";
                for (Date date : list) {
                    dateStr = DateUtil.YYYY_MM_DD.format(date);
                    exchangeStatistics(dateStr, date);
                }
            } else {
                Date date = DateUtil.dateAddDay(DateUtil.getCurrentDate(), -1);
                String dateStr = DateUtil.getFormatTime(DateUtil.YYYY_MM_DD, date);
                exchangeStatistics(dateStr, date);
            }

        } catch (ParseException e) {
            log.error("Date parsing exception", e);
        }
    }

    private void statisticsMember(String dateStr, Date date) throws ParseException {
        log.info("Start counting member information{}", dateStr);
        int registrationNum = memberDao.getRegistrationNum(dateStr);
        int businessCount = memberDao.getBussinessNum(dateStr);
        int applicationNum = memberDao.getApplicationNum(dateStr);
        MemberLog memberLog = new MemberLog();
        memberLog.setApplicationNum(applicationNum);
        memberLog.setBussinessNum(businessCount);
        memberLog.setRegistrationNum(registrationNum);
        memberLog.setDate(DateUtil.YYYY_MM_DD.parse(dateStr));
        memberLog.setYear(DateUtil.getDatePart(date, Calendar.YEAR));
        // Calendar month starts from 0 by default, for convenience, save month starts from 1
        memberLog.setMonth(DateUtil.getDatePart(date, Calendar.MONTH) + 1);
        memberLog.setDay(DateUtil.getDatePart(date, Calendar.DAY_OF_MONTH));
        log.info("{}Member Information{}", dateStr, memberLog);
        memberLogDao.save(memberLog);
        log.info("End statistics on member information{}", dateStr);
    }

    private List<Date> getDateList() throws ParseException {
        List<Date> list = new ArrayList<>();

        Date date = memberDao.getStartRegistrationDate();
        String dateStr = DateUtil.YYYY_MM_DD.format(date);
        date = DateUtil.YYYY_MM_DD.parse(dateStr);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Date endDate = DateUtil.dateAddDay(new Date(), -1);
        while (date.before(endDate)) {
            list.add(date);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            date = calendar.getTime();
        }
        return list;
    }

    private void statisticsFee(String dateStr, Date date) throws ParseException {
        /**
         * Franchise currency transaction
         *
         */
        log.info("Start counting fiat currency transaction information{}", dateStr);
        List<Object[]> list1 = orderService.getOtcOrderStatistics(dateStr);
        TurnoverStatistics turnoverStatistics = new TurnoverStatistics();
        turnoverStatistics.setDate(DateUtil.YYYY_MM_DD.parse(dateStr));
        turnoverStatistics.setYear(DateUtil.getDatePart(date, Calendar.YEAR));
        // Calendar month starts from 0 by default, for convenience, save month starts from 1
        turnoverStatistics.setMonth(DateUtil.getDatePart(date, Calendar.MONTH) + 1);
        turnoverStatistics.setDay(DateUtil.getDatePart(date, Calendar.DAY_OF_MONTH));
        for (Object[] objects : list1) {
            /**
             * Franchise currency transaction volume/process fee
             */
            turnoverStatistics.setUnit(objects[0].toString());
            turnoverStatistics.setAmount((BigDecimal) objects[2]);
            turnoverStatistics.setFee((BigDecimal) objects[3]);
            turnoverStatistics.setType(TransactionTypeEnum.OTC_NUM);
            log.info("{}Franchise transaction information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");

            /**
             * Franchise currency transaction volume
             */
            turnoverStatistics.setAmount((BigDecimal) objects[4]);
            turnoverStatistics.setType(TransactionTypeEnum.OTC_MONEY);
            turnoverStatistics.setFee(null);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on fiat currency transaction information{}", dateStr);

        /**
         * Coin transaction volume
         */
        log.info("Start counting currency transaction volume information{}", dateStr);
        turnoverStatistics.setFee(null);
        List<Object[]> list2 = exchangeOrderRepository.getExchangeTurnoverBase(dateStr);
        for (Object[] objects : list2) {
            turnoverStatistics.setUnit(objects[0].toString());
            turnoverStatistics.setAmount((BigDecimal) objects[2]);
            turnoverStatistics.setType(TransactionTypeEnum.EXCHANGE_BASE);
            log.info("{}Coin transaction volume information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on currency transaction volume information{}", dateStr);

        /**
         * Coin transaction volume
         */
        log.info("Start counting currency trading volume information{}", dateStr);
        List<Object[]> list3 = exchangeOrderRepository.getExchangeTurnoverCoin(dateStr);
        for (Object[] objects : list3) {
            turnoverStatistics.setUnit(objects[0].toString());
            turnoverStatistics.setAmount((BigDecimal) objects[2]);
            turnoverStatistics.setType(TransactionTypeEnum.EXCHANGE_COIN);
            log.info("{}Coin trading volume information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on currency trading volume information{}", dateStr);

        /**
         * Recharge coins
         */
        log.info("Start counting the recharge information{}", dateStr);
        List<Object[]> list4 = memberDepositDao.getDepositStatistics(dateStr);
        for (Object[] objects : list4) {
            turnoverStatistics.setAmount(new BigDecimal(objects[1].toString()));
            turnoverStatistics.setUnit(objects[0].toString());
            turnoverStatistics.setType(TransactionTypeEnum.RECHARGE);
            log.info("{}Coin recharge information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on recharge information{}", dateStr);

        /**
         * Coin transaction fee
         */
        log.info("Start counting coin transaction fee information{}", dateStr);
        ProjectionOperation projectionOperation = Aggregation.project("time", "type", "unit", "fee");

        Criteria operator = Criteria.where("coinName").ne("").andOperator(
                Criteria.where("time").gte(DateUtil.YYYY_MM_DD_MM_HH_SS.parse(dateStr + " 00:00:00").getTime()),
                Criteria.where("time").lte(DateUtil.YYYY_MM_DD_MM_HH_SS.parse(dateStr + " 23:59:59").getTime()),
                Criteria.where("type").is("EXCHANGE")
        );

        MatchOperation matchOperation = Aggregation.match(operator);

        GroupOperation groupOperation = Aggregation.group("unit", "type").sum("fee").as("feeSum");

        Aggregation aggregation = Aggregation.newAggregation(projectionOperation, matchOperation, groupOperation);
        // Perform an action
        AggregationResults<Map> aggregationResults = this.mongoTemplate.aggregate(aggregation, "order_detail_aggregation", Map.class);
        List<Map> list = aggregationResults.getMappedResults();
        for (Map map : list) {
            log.info("*********{}Coin transaction fee{}************", dateStr, map);
            turnoverStatistics.setFee(new BigDecimal(map.get("feeSum").toString()));
            turnoverStatistics.setAmount(null);
            turnoverStatistics.setUnit(map.get("unit").toString());
            turnoverStatistics.setType(TransactionTypeEnum.EXCHANGE);
            log.info("{}Coin transaction fee information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on currency transaction fee information{}", dateStr);

        /**
         * Withdrawal of coins
         */
        log.info("Start counting withdrawal information{}", dateStr);
        List<Object[]> list5 = withdrawRecordDao.getWithdrawStatistics(dateStr);
        for (Object[] objects : list5) {
            turnoverStatistics.setFee(new BigDecimal(objects[2].toString()));
            turnoverStatistics.setAmount(new BigDecimal(objects[1].toString()));
            turnoverStatistics.setUnit(objects[0].toString());
            turnoverStatistics.setType(TransactionTypeEnum.WITHDRAW);
            log.info("{}Withdrawal information{}", dateStr, turnoverStatistics);
            mongoTemplate.insert(turnoverStatistics, "turnover_statistics");
        }
        log.info("End statistics on withdrawal information{}", dateStr);
    }

    private void exchangeStatistics(String dateStr, Date date) throws ParseException {
        /**
         * Coin transactions (according to transaction pair statistics)
         */
        log.info("Start counting currency transactions (by transaction pair statistics) information {}", dateStr);
        List<Object[]> list = exchangeOrderRepository.getExchangeTurnoverSymbol(dateStr);
        ExchangeTurnoverStatistics exchangeTurnoverStatistics = new ExchangeTurnoverStatistics();
        for (Object[] objects : list) {
            exchangeTurnoverStatistics.setDate(DateUtil.YYYY_MM_DD.parse(dateStr));
            exchangeTurnoverStatistics.setAmount((BigDecimal) objects[3]);
            exchangeTurnoverStatistics.setBaseSymbol((String) objects[0]);
            exchangeTurnoverStatistics.setCoinSymbol((String) objects[1]);
            exchangeTurnoverStatistics.setMoney((BigDecimal) objects[4]);
            exchangeTurnoverStatistics.setYear(DateUtil.getDatePart(date, Calendar.YEAR));
            // Calendar month starts from 0 by default, for convenience, save month starts from 1
            exchangeTurnoverStatistics.setMonth(DateUtil.getDatePart(date, Calendar.MONTH) + 1);
            exchangeTurnoverStatistics.setDay(DateUtil.getDatePart(date, Calendar.DAY_OF_MONTH));
            log.info("{}Coin transactions (according to transaction pair statistics) information {}", dateStr, exchangeTurnoverStatistics);
            mongoTemplate.insert(exchangeTurnoverStatistics, "exchange_turnover_statistics");
        }
        log.info("End statistics currency transaction (according to transaction pair statistics) information {}", dateStr);
    }

}
