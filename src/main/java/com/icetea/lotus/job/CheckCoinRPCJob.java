package com.icetea.lotus.job;

import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.util.MessageResult;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CheckCoinRPCJob {

    List<Coin> changeCoinList = new ArrayList<Coin>(); // Block height change currency type
    List<Coin> noChangeCoinList = new ArrayList<Coin>(); // Block height has not changed
    List<Coin> noticeCoinList = new ArrayList<Coin>(); // Currencies to note
    @Autowired
    private JavaMailSender javaMailSender;
    @Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;
    @Value("${spark.system.admins}")
    private String admins;
    @Value("${spark.system.admin-phones}")
    private String adminPhones;
    @Autowired
    private CoinService coinService;
    @Autowired
    private RestTemplate restTemplate;
    private List<Coin> lastCoinList = null;
    private boolean sendFlag = false; // Whether to send a report

    @Scheduled(cron = "0 */30 * * * *")
    public void checkIfHasExpiredOrder() {

        changeCoinList.clear();
        noChangeCoinList.clear();
        noticeCoinList.clear();

        List<Coin> coinList = coinService.findAll();
        for (Coin coin : coinList) {
            String url2 = "http://SERVICE-RPC-" + coin.getUnit() + "/rpc/height";
            if (coin.getEnableRpc().getOrdinal() == 1) {
                coin.setBlockHeight(getRPCBlockHeight(url2, coin.getUnit()));
            } else {
                coin.setBlockHeight(Long.valueOf(0));
            }
        }

        // First run
        if (lastCoinList == null) {
            this.lastCoinList = coinList;
            // The initialization status has not changed.
            for (Coin coin : coinList) {
                noChangeCoinList.add(coin);
            }
        } else {
            // Check whether there is any change in the last height and this time
            for (Coin coin : coinList) {
                for (Coin coinLast : lastCoinList) {
                    if (coin.getUnit().equals(coinLast.getUnit())) {
                        if (coin.getBlockHeight() != coinLast.getBlockHeight()) {
                            changeCoinList.add(coin);
                        } else {
                            noChangeCoinList.add(coin);
                            // The block height has not changed, but RPC is enabled, which means that the block has not been synchronized for a long time, which needs to be paid attention to.
                            if (coin.getEnableRpc().getOrdinal() == 1) {
                                noticeCoinList.add(coin);
                            }
                        }
                    }
                }
            }
            this.lastCoinList = coinList;
        }

        if (noticeCoinList.size() > 0) {
            // Send email notification
            String[] adminList = admins.split(",");
            for (int i = 0; i < adminList.length; i++) {
                try {
                    sendEmailMsg(adminList[i], changeCoinList, noChangeCoinList, noticeCoinList, "Currency RPC health check report");
                } catch (MessagingException | IOException | TemplateException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Long getRPCBlockHeight(String url, String unit) {
        try {
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String height = mr.getData().toString();
                    Long longHeight = Long.valueOf(height);
                    return longHeight;
                }
            }
        } catch (IllegalStateException e) {
            log.error("error={}", e);
            return Long.valueOf(0);
        } catch (Exception e) {
            log.error("error={}", e);
            return Long.valueOf(0);
        }
        return Long.valueOf(0);
    }

    @Async
    public void sendEmailMsg(String email, List<Coin> changeList, List<Coin> nochangeList, List<Coin> noticeList, String subject) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        helper = new MimeMessageHelper(mimeMessage, true);
        helper.setFrom(from);
        helper.setTo(email);
        helper.setSubject(company + "-" + subject);
        Map<String, Object> model = new HashMap<>(16);
        model.put("changeList", changeList);
        model.put("nochangeList", nochangeList);
        model.put("noticeList", noticeList);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), "/templates");
        Template template = cfg.getTemplate("coinCheck.ftl");
        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        // Send an email
        javaMailSender.send(mimeMessage);
    }
}
