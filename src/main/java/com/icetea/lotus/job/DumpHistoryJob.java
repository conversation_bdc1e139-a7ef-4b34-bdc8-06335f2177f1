package com.icetea.lotus.job;

import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDetail;
import com.icetea.lotus.service.ExchangeOrderDetailService;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.MemberTransactionService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.*;

/**
 * Clean up robot orders (order 5 days ago)
 * <AUTHOR>
 */
@Component
@Slf4j
public class DumpHistoryJob {

	@Autowired
	private ExchangeOrderService exchangeOrderService;

    @Autowired
    private ExchangeOrderDetailService exchangeOrderDetailService;

    @Autowired
    private MemberTransactionService memberTransactionService;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private MongoTemplate mongoTemplate ;

    @Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;

    @Value("${spark.system.admins}")
    private String admins;
    /**
     * Perform once every day at 3:20
     * Note that when deleting the main order table and the details table, only the robot data is deleted (see the SQL implementation of the data layer for details)
     * */
	@Scheduled(cron = "0 20 03 * * *")
	public void deleteHistoryOrders(){
		log.info("Start cleaning up transaction history data");
		long beforeTime = System.currentTimeMillis() - (2 * 24 * 60 * 60 * 1000); // 2 days ago
		log.info("Clear orders before the specified time:"+beforeTime);
		int limit = 1000;
		int deleteTime = 1;
		int deleteCount = 0;
		List<ExchangeOrder> list = exchangeOrderService.queryHistoryDelete(beforeTime,limit);
		boolean hashNext =true;
		while (hashNext) {
			hashNext = false;
			if (list != null && list.size() > 0) {
				deleteCount = deleteCount + list.size();
				Set<String> orderIds = new HashSet<>();
				for (int i = 0; i < list.size(); i++) {
					ExchangeOrder exchangeOrder = list.get(i);
					// Clear transaction details in mongodb
					orderIds.add(exchangeOrder.getOrderId());
// log.info("Clean order details:" + exchangeOrder.getOrderId());
// exchangeOrderService.delete(exchangeOrder.getOrderId());

				}
				// Batch Delete
				if(orderIds.size()>0) {
					log.info("Number of orders for batch cleaning:" + orderIds.size());
					Query query = new Query(Criteria.where("orderId").in(orderIds));
					log.info("Start cleaning up mongo:" + orderIds.size());
					mongoTemplate.remove(query, ExchangeOrderDetail.class);
					log.info("Start cleaning the database:" + orderIds.size());
					exchangeOrderService.deleteInBatch(list);
					log.info("Cleaning is complete:" + orderIds.size());
				}

				log.info("The {} round of clearing data is completed, and {} pieces of data have been cleared",deleteTime,deleteCount);
				deleteTime++;
				if (list.size() == limit) {
					hashNext = true;
					log.info("Obtain the data in the {} round and clean it up",deleteTime);
					list = exchangeOrderService.queryHistoryDelete(beforeTime,limit);
				}
			}
		}
		log.info("Clear the transaction order:" + deleteCount + "strip");

		Date today = new Date();
		Calendar now =Calendar.getInstance();
		now.setTime(today);
		now.set(Calendar.DATE,now.get(Calendar.DATE) - 5); // Clean up data 5 days ago
		Date startTime = now.getTime();

		log.info("Clear asset change record time:" + startTime.getTime());

		int tCount = memberTransactionService.deleteHistory(startTime);
		memberTransactionService.deleteWalletHistory(startTime);

		log.info("Clear asset change records:" + tCount);
		// Send notification email
// String[] adminList = admins.split(",");
// for(int i = 0; i < adminList.length; i++) {
// try {
// sendEmailMsg(adminList[i], "Clean robot order (total" + deleteCount+ "bars),"
// + "Clean the robot asset record total ("+tCount+")", "Clean the transaction order");
// } catch (MessagingException e) {
// e.printStackTrace();
// } catch (IOException e) {
// e.printStackTrace();
// } catch (TemplateException e) {
// e.printStackTrace();
// }
// }

		log.info("End cleaning of transaction history data");
	}
    // Delete data in asset changes (retained for 9 days, code deleted)
    // delete from member_transaction where create_time < '2019-10-15 14:22:49' and type = 3 and member_id = 1

    // Delete wallet historical change data (retained for 9 days, manually deleted)
    // delete from member_wallet_history where op_time < '2019-10-15 14:22:49' and member_id=1

    @Async
    public void sendEmailMsg(String email, String msg, String subject) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        helper = new MimeMessageHelper(mimeMessage, true);
        helper.setFrom(from);
        helper.setTo(email);
        helper.setSubject(company + "-" + subject);
        Map<String, Object> model = new HashMap<>(16);
        model.put("msg", msg);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), "/templates");
        Template template = cfg.getTemplate("simpleMessage.ftl");
        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        // Send an email
        javaMailSender.send(mimeMessage);
    }
}
