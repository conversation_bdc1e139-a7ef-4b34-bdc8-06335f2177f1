package com.icetea.lotus.job;

import com.icetea.lotus.service.WithdrawRecordService;
import com.icetea.lotus.service.impl.notification.EmailService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Check user withdrawal application
 *
 * <AUTHOR>
@Component
@Slf4j
public class CheckWithdrawJob {

    private final WithdrawRecordService withdrawRecordService;
    private final SMSProvider smsProvider;
    private final EmailService emailService;

    public CheckWithdrawJob(WithdrawRecordService withdrawRecordService, SMSProvider smsProvider, EmailService emailService) {
        this.withdrawRecordService = withdrawRecordService;
        this.smsProvider = smsProvider;
        this.emailService = emailService;
    }

    @Value("${spark.system.admins}")
    private String admins;

    @Value("${spark.system.admin-phones}")
    private String adminPhones;

    /**
     * Check once an hourly
     */
    @Scheduled(cron = "0 0 * * * *")
    public void checkNewWithdrawApplication() {

        long count = withdrawRecordService.countAuditing();
        if (count > 0) {
            try {
                String[] adminList = admins.split(",");
                for (String s : adminList) {
                    emailService.sendEmailMsg(
                            s,
                            "There are new withdrawal applications (total" + count + "strip )",
                            "New withdrawal review notice"
                    );
                }
            } catch (Exception e) {
                processCatchException(e);
            }
        }
    }

    private void processCatchException(Exception e) {
        MessageResult result;
        try {
            String[] phones = adminPhones.split(",");
            if (phones.length > 0) {
                result = smsProvider.sendSingleMessage(phones[0], "==New withdrawal application==");
                if (result.getCode() != 0) {
                    if (phones.length > 1) {
                        smsProvider.sendSingleMessage(phones[1], "==New withdrawal application==");
                    }
                }
            }

        } catch (Exception e1) {
            e1.printStackTrace();
        }
        e.printStackTrace();
    }
}