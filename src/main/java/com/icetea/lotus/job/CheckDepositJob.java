package com.icetea.lotus.job;

import com.icetea.lotus.service.MemberDepositService;
import com.icetea.lotus.service.impl.notification.EmailService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class CheckDepositJob {

    private final MemberDepositService memberDepositService;
    private final SMSProvider smsProvider;
    private final EmailService emailService;

	public CheckDepositJob(
			MemberDepositService memberDepositService,
			SMSProvider smsProvider,
			EmailService emailService
	){
		this.memberDepositService = memberDepositService;
		this.smsProvider = smsProvider;
		this.emailService = emailService;
	}


    @Value("${spark.system.admins}")
    private String admins;
    
    @Value("${spark.system.admin-phones}")
    private String adminPhones;
    
    private Long maxDepositId = 0L;
    
	/**
	 * Check once an hourly
	 */
	@Scheduled(cron = "0 0 * * * *")
    public void checkNewWithdrawApplication(){
		// Get the recharge record
		Long currentDepositId = memberDepositService.getMaxId();
		if(currentDepositId == null) return;
		
		if(this.maxDepositId.compareTo(0L) != 0) {
			if(currentDepositId.compareTo(this.maxDepositId) > 0) {
				var count = currentDepositId - this.maxDepositId;
				this.maxDepositId = currentDepositId;
				
				try {
					String[] adminList = admins.split(",");
                    for (String s : adminList) {
                         emailService.sendEmailMsg(
								s,
								"There is a new recharge (total" + count + "strip )",
								"User recharge notification"
						);
                    }
				} catch (Exception e) {
					processWithException(e);
				}
			}
		} else {
			this.maxDepositId = currentDepositId;
		}
	}
	

	private void processWithException(Exception e) {
		MessageResult result;
		try {
			String[] phones = adminPhones.split(",");
			if(phones.length > 0) {
				result = smsProvider.sendSingleMessage(phones[0], "==User Recharge==");
				if(result.getCode() != 0 && phones.length > 1) {
					smsProvider.sendSingleMessage(phones[1], "==User Recharge==");
				}
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		e.printStackTrace();
	}
}
