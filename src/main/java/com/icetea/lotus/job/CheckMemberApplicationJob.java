package com.icetea.lotus.job;


import com.icetea.lotus.service.MemberApplicationService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Check real-name authentication applicants
 * <AUTHOR>
 */
@Component
@Slf4j
public class CheckMemberApplicationJob {
	@Autowired
	private MemberService memberService;
	
	@Autowired
    private MemberApplicationService memberApplicationService;
	
	@Autowired
    private SMSProvider smsProvider;
	
	@Autowired
    private JavaMailSender javaMailSender;
	
	@Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;
    
	@Value("${spark.system.admins}")
    private String admins;

    @Value("${spark.system.admin-phones}")
    private String adminPhones;
    
    private Long maxUserId = Long.valueOf(0);
    
	/**
	 * Check once an hourly
	 */
//	@Scheduled(cron = "0 0 * * * *")
    public void checkNewMemberApplication(){
		if(isRestTime()) {
			return;
		}
		// Query the number to be reviewed
		Long count = memberApplicationService.countAuditing();
		if(count > 0) {
			try {
				String[] adminList = admins.split(",");
				for(int i = 0; i < adminList.length; i++) {
					sendEmailMsg(adminList[i], "There is a new real-name authentication application (total" + count+ "strip )", "New real-name authentication review notice");
				}
			} catch (Exception e) {
				MessageResult result;
				try {
					String[] phones = adminPhones.split(",");
					if(phones.length > 0) {
						result = smsProvider.sendSingleMessage(phones[0], "==New real-name application==");
						if(result.getCode() != 0) {
							if(phones.length > 1) {
								smsProvider.sendSingleMessage(phones[1], "==New real-name application==");
							}
						}
					}
					
				} catch (Exception e1) {
					e1.printStackTrace();
				}
				e.printStackTrace();
			}
		}
	}
	
	@Async
    public void sendEmailMsg(String email, String msg, String subject) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        helper = new MimeMessageHelper(mimeMessage, true);
        helper.setFrom(from);
        helper.setTo(email);
        helper.setSubject(company + "-" + subject);
        Map<String, Object> model = new HashMap<>(16);
        model.put("msg", msg);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), "/templates");
        Template template = cfg.getTemplate("simpleMessage.ftl");
        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        // Send an email
        javaMailSender.send(mimeMessage);
        log.info("send email for {},content:{}", email, html);
    }
	
	/**
	 * Check once an hourly (whether new users are registered)
	 */
	@Scheduled(cron = "0 0 * * * *")
    public void checkHasNewUser(){
		if(isRestTime()) {
			return;
		}
		// Query the number to be reviewed
		Long currentMaxId = memberService.getMaxId();
		if(currentMaxId == null) return;
		
		if(this.maxUserId.compareTo(Long.valueOf(0)) != 0) {
			if(currentMaxId.compareTo(this.maxUserId) > 0) {
				// There are new users
				Long userCount = currentMaxId - this.maxUserId;
				this.maxUserId = currentMaxId;
				
				try {
					String[] adminList = admins.split(",");
					for(int i = 0; i < adminList.length; i++) {
						sendEmailMsg(adminList[i], "New users have registered (total" + userCount+ "people )", "New user registration notice");
					}
				} catch (Exception e) {
					MessageResult result;
					try {
						String[] phones = adminPhones.split(",");
						if(phones.length > 0) {
							result = smsProvider.sendSingleMessage(phones[0], "==New user registration("+userCount+")==");
							if(result.getCode() != 0) {
								if(phones.length > 1) {
									smsProvider.sendSingleMessage(phones[1], "==New user registration("+userCount+")==");
								}
							}
						}
					} catch (Exception e1) {
						e1.printStackTrace();
					}
					e.printStackTrace();
				}
			}
		}else {
			this.maxUserId = currentMaxId;
		}
	}
	
	private boolean isRestTime() {
		Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // Different trading volumes are required for different time periods of the day
        
        if(hour >= 0 && hour <= 6) {
        	return true;
        }
        return false;
	}
}
