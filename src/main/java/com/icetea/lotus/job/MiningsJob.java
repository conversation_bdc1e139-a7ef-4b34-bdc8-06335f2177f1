package com.icetea.lotus.job;

import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.MiningOrder;
import com.icetea.lotus.entity.spot.MiningOrderDetail;
import com.icetea.lotus.service.*;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.vendor.provider.SMSProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MiningsJob {
	
	@Autowired
    private SMSProvider smsProvider;

	@Autowired
	private MiningOrderDetailService miningOrderDetailService;
	
	@Autowired
	private MiningOrderService miningOrderService;

	@Autowired
	private MemberWalletService memberWalletService;
	
	@Autowired
	private MemberTransactionService memberTransactionService;
	
	@Autowired
	private MemberService memberService;
	
	/**
	 * Miners' income is paid every night at 10:30
	 */
	@Scheduled(cron = "0 30 22 * * *")
	public void minings() {
		List<MiningOrder> list = miningOrderService.findAllByMiningStatus(1);
		
		Date currentDate = DateUtil.getCurrentDate();
		for(MiningOrder item : list) {
			if(currentDate.compareTo(item.getEndTime()) < 0) {
				Member member = memberService.findOne(item.getMemberId());
				// Generate profits
				MemberWallet userWallet = memberWalletService.findByCoinUnitAndMemberId(item.getMiningUnit(), item.getMemberId());
				if(userWallet != null) {
					// Funding records
					MemberTransaction memberTransaction1 = new MemberTransaction();
					memberTransaction1.setFee(BigDecimal.ZERO);
					memberTransaction1.setAmount(item.getCurrentDaysprofit());
					memberTransaction1.setMemberId(item.getMemberId());
			        memberTransaction1.setSymbol(item.getMiningUnit());
			        memberTransaction1.setType(TransactionType.ACTIVITY_BUY);
			        memberTransaction1.setCreateTime(DateUtil.getCurrentDate());
			        memberTransaction1.setRealFee("0");
			        memberTransaction1.setDiscountFee("0");
			        memberTransaction1 = memberTransactionService.save(memberTransaction1);
			        // Update balance
			        userWallet.setBalance(userWallet.getBalance().add(item.getCurrentDaysprofit()));
			        memberWalletService.save(userWallet);
			        
			        // Update mining machine data
			        item.setTotalProfit(item.getTotalProfit().add(item.getCurrentDaysprofit()));
			        item.setMiningedDays(item.getMiningedDays() + 1);
			        miningOrderService.saveAndFlush(item);
			        
			        // Add mining machine output data
			        MiningOrderDetail detail = new MiningOrderDetail();
			        detail.setMemberId(item.getMemberId());
			        detail.setCreateTime(DateUtil.getCurrentDate());
			        detail.setMiningOrderId(item.getId());
			        detail.setMiningUnit(item.getMiningUnit());
			        detail.setOutput(item.getCurrentDaysprofit());
			        
			        miningOrderDetailService.saveAndFlush(detail);
			        
			        // Send a SMS to notify the user
			        try {
		 				smsProvider.sendCustomMessage(member.getMobilePhone(), "Dear user, your mining machine【"+ item.getTitle() + "】Today's mining output:" + item.getCurrentDaysprofit() + " "+item.getMiningUnit() + "，Please check it!");
		 			} catch (Exception e) {
		 				e.printStackTrace();
		 			}
				}
			}
		}
	}
}
