package com.icetea.lotus;

import com.icetea.lotus.config.SecurityConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@ComponentScan(
        basePackages = "com.icetea", // hoặc base package của bạn
        excludeFilters = @ComponentScan.Filter(
                type = FilterType.ASSIGNABLE_TYPE,
                classes = {
                        com.icetea.lotus.config.oauth2.OAuth2RestTemplateFactory.class,
                        com.icetea.lotus.config.oauth2.OAuth2RestTemplateConfig.class,
                }
        )
)
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients
@ImportAutoConfiguration(exclude = SecurityConfiguration.class)
@ConfigurationPropertiesScan
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
