package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.AnnouncementClassification;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.AnnouncementService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.system.ExtendedAnnouncementService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * The type Announcement controller.
 */
@RestController
@RequestMapping("system/announcement")
@RequiredArgsConstructor
public class AnnouncementController extends BaseController {
    @Autowired
    private AnnouncementService announcementService;
    @Autowired
    private LocaleMessageSourceService messageSource;

    private final ExtendedAnnouncementService extendedAnnouncementService;

    /**
     * Create message result.
     *
     * @param title                      the title
     * @param content                    the content
     * @param lang                       the lang
     * @param announcementClassification the announcement classification
     * @param isShow                     the is show
     * @param imgUrl                     the img url
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:create')")
    @PostMapping("create")
    public MessageResult create(
            @RequestParam String title,
            @RequestParam String content,
            @RequestParam String lang,
            @RequestParam AnnouncementClassification announcementClassification,
            @RequestParam("isShow") Boolean isShow,
            @RequestParam(value = "imgUrl", required = false) String imgUrl) {

        return extendedAnnouncementService.create(title, content, lang, announcementClassification, isShow, imgUrl);
    }

    /**
     * To top message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:top')")
    @PostMapping("top")
    @AccessLog(module = AdminModule.CMS, operation = "Announcement topped")
    public MessageResult toTop(@RequestParam("id")long id){
        return extendedAnnouncementService.toTop(id);
    }


    /**
     * Cancel the announcement top
     *
     * @param id the id
     * @return message result
     */
    @PreAuthorize("hasRole('system:announcement:dwon')")
    @PostMapping("down")
    @AccessLog(module = AdminModule.CMS, operation = "Announcement cancels the top")
    public MessageResult toDown(@RequestParam("id")long id){

        return extendedAnnouncementService.toDown(id);
    }

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param isShow    the is show
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:page-query')")
    @GetMapping("page-query")
    public MessageResult page(
            PageModel pageModel,
            @RequestParam(required = false) Boolean isShow) {

        return extendedAnnouncementService.page(pageModel, isShow);
    }

    /**
     * Delete one message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:deletes')")
    @PatchMapping("deletes")
    public MessageResult deleteOne(@RequestParam("ids") Long[] ids) {

        return extendedAnnouncementService.deleteOne(ids);
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:detail')")
    @GetMapping("{id}/detail")
    public MessageResult detail(
            @PathVariable Long id) {

        return extendedAnnouncementService.detail(id);
    }


    /**
     * Update message result.
     *
     * @param id                         the id
     * @param title                      the title
     * @param content                    the content
     * @param isShow                     the is show
     * @param lang                       the lang
     * @param announcementClassification the announcement classification
     * @param imgUrl                     the img url
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:update')")
    @PutMapping("{id}/update")
    public MessageResult update(
            @PathVariable Long id,
            @RequestParam String title,
            @RequestParam String content,
            @RequestParam Boolean isShow,
            @RequestParam String lang,
            @RequestParam AnnouncementClassification announcementClassification,
            @RequestParam(value = "imgUrl", required = false) String imgUrl) {

        return extendedAnnouncementService.update(id, title, content, isShow, lang, announcementClassification, imgUrl);
    }

    /**
     * Turn off message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:turn-off')")
    @PatchMapping("{id}/turn-off")
    public MessageResult turnOff(@PathVariable Long id) {

        return extendedAnnouncementService.turnOff(id);
    }

    /**
     * Turn on message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:announcement:turn-on')")
    @PatchMapping("{id}/turn-on")
    public MessageResult turnOn(@PathVariable("id") Long id) {

        return extendedAnnouncementService.turnOn(id);

    }

}
