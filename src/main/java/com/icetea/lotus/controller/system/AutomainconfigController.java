package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.AutomainSetPassword;
import com.icetea.lotus.entity.spot.Automainconfig;
import com.icetea.lotus.entity.spot.MessageEncrypt;
import com.icetea.lotus.service.system.ExtendedAutomainconfigService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Currency expansion management
 */
@Slf4j
@RestController
@RequestMapping("/system/automainconfig")
@RequiredArgsConstructor
public class AutomainconfigController {

    private final ExtendedAutomainconfigService extendedAutomainconfigService;


    /**
     * Coin list message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:coin-list')")
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the currency list in the collection configuration")
    public MessageResult coinList() {

        return extendedAutomainconfigService.coinList();
    }

    /**
     * Protocol list message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:protocol-list')")
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the currency protocol list in the collection configuration")
    public MessageResult protocolList() {

        return extendedAutomainconfigService.protocolList();
    }

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the collection configuration list")
    public MessageResult pageQuery(PageModel pageModel) {

        return extendedAutomainconfigService.pageQuery(pageModel);
    }

    /**
     * Merge message result.
     *
     * @param automainconfig the automainconfig
     * @param bindingResult  the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:merge')")
    @PostMapping("/merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create/modify collection configuration")
    public MessageResult merge(@Valid Automainconfig automainconfig, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedAutomainconfigService.merge(automainconfig);
    }

    /**
     * Collect coin message result.
     *
     * @param automainconfig the automainconfig
     * @param bindingResult  the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:collect-coin')")
    @PostMapping("/collectCoin")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Manual collection")
    public MessageResult collectCoin(@Valid Automainconfig automainconfig, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedAutomainconfigService.collectCoin(automainconfig);
    }

    /**
     * Sets password.
     *
     * @param automainconfig the automainconfig
     * @return the password
     */
    @PreAuthorize("hasRole('system:automainconfig:set-password')")
    @PostMapping("/setPassword")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Set password")
    public MessageResult setPassword(AutomainSetPassword automainconfig) {

        return extendedAutomainconfigService.setPassword(automainconfig);

    }

    /**
     * Update contract message result.
     *
     * @param automainconfig the automainconfig
     * @return the message result
     */
    @PreAuthorize("hasRole('system:automainconfig:update-contract')")
    @PostMapping("/updateContract")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Synchronous currency")
    public MessageResult updateContract(AutomainSetPassword automainconfig) {

        return extendedAutomainconfigService.updateContract(automainconfig);
    }

    /**
     * Encrypt message result.
     *
     * @param messageEncrypt the message encrypt
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('system:automainconfig:encrypt')")
    @PostMapping("/encrypt")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Encryption tools")
    public MessageResult encrypt(MessageEncrypt messageEncrypt) throws Exception {
        return extendedAutomainconfigService.encrypt(messageEncrypt);
    }


}
