package com.icetea.lotus.controller.system;


import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.service.system.GoogleVerificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;


import static com.icetea.lotus.constant.SysConstant.SESSION_MEMBER;

/**
 * The type Google verification controller.
 *
 * <AUTHOR> @time 2020.04.09 11:07
 */
@RestController
@Slf4j
@RequestMapping("/google")
@RequiredArgsConstructor
public class GoogleVerificationController {
    private final GoogleVerificationService googleVerificationService;

    /**
     * Verify Google
     *
     * @param user  the user
     * @param codes the codes
     * @return true message result
     * <AUTHOR> @time 2020.04.09 11:36
     */

    @RequestMapping(value = "/yzgoogle",method = RequestMethod.GET)
    public MessageResult yzgoogle(@SessionAttribute(SESSION_MEMBER) AuthMember user, String codes) {
        return googleVerificationService.yzgoogle(user, codes);
    }


    /**
     * Generate Google Certification Code
     *
     * @param user the user
     * @return message result
     */
    @RequestMapping(value = "/sendgoogle",method = RequestMethod.GET)
    public MessageResult  sendgoogle(@SessionAttribute(SESSION_MEMBER) AuthMember user) {
        return googleVerificationService.sendgoogle(user);
    }


    /**
     * Google Unbinding
     *
     * @param codes    the codes
     * @param user     the user
     * @param password the password
     * @return true message result
     * <AUTHOR> @time 2020.04.09 12:47
     */
    @RequestMapping(value = "/jcgoogle" ,method = RequestMethod.POST)
    public MessageResult jcgoogle(String codes, @SessionAttribute(SESSION_MEMBER) AuthMember user,String password) {
        return googleVerificationService.jcgoogle(codes, user, password);
    }




    // ga.setWindowSize(0); // should give 5 * 30 seconds of grace...

    /**
     * Bind Google
     *
     * @param codes  the codes
     * @param user   the user
     * @param secret the secret
     * @return true message result
     * <AUTHOR> @time 2020.04.09 15:19
     */
    @RequestMapping(value = "/googleAuth" ,method = RequestMethod.POST)
    public MessageResult googleAuth(String codes, @SessionAttribute(SESSION_MEMBER) AuthMember user,String secret) {
        return googleVerificationService.googleAuth(codes, user, secret);
    }
}
