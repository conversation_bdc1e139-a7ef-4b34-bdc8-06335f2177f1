package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.system.ExtendedCoinprotocolService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Currency Agreement Management
 */
@Slf4j
@RestController
@RequestMapping("/system/coinprotocol")
@RequiredArgsConstructor
public class CoinProtocolController {

    private final ExtendedCoinprotocolService extendedCoinprotocolService;

    @Autowired
    private CoinprotocolService coinprotocolService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coinprotocol:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get a list of all protocols")
    public MessageResult pageQuery(PageModel pageModel) {

        return extendedCoinprotocolService.pageQuery(pageModel);
    }

    /**
     * Merge message result.
     *
     * @param coinprotocol  the coinprotocol
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coinprotocol:merge')")
    @PostMapping("/merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create/modify protocol")
    public MessageResult merge(@Valid Coinprotocol coinprotocol, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        return extendedCoinprotocolService.merge(coinprotocol);
    }



}
