package com.icetea.lotus.controller.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.AppRevision;
import com.icetea.lotus.model.create.AppRevisionCreate;
import com.icetea.lotus.model.update.AppRevisionUpdate;
import com.icetea.lotus.service.AppRevisionService;
import com.icetea.lotus.service.system.ExtendedAppRevisionService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * The type App revision controller.
 */
@RestController
@RequestMapping("system/app-revision")
@RequiredArgsConstructor
public class AppRevisionController {

    private final ExtendedAppRevisionService extendedAppRevisionService;

    /**
     * Create message result.
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @return the message result
     */
// New
    @PostMapping
    public MessageResult create(@Valid AppRevisionCreate model, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedAppRevisionService.create(model);
    }

    /**
     * Put message result.
     *
     * @param id    the id
     * @param model the model
     * @return the message result
     */
// renew
    @PutMapping("{id}")
    public MessageResult put(@PathVariable("id") Long id, AppRevisionUpdate model) {
        return extendedAppRevisionService.put(id, model);
    }

    /**
     * Get message result.
     *
     * @param id the id
     * @return the message result
     */
// Details
    @GetMapping("{id}")
    public MessageResult get(@PathVariable("id") Long id) {
        return extendedAppRevisionService.get(id);
    }

    /**
     * Get message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
// Pagination
    @GetMapping("page-query")
    public MessageResult get(PageModel pageModel) {
        return extendedAppRevisionService.get(pageModel);
    }
}
