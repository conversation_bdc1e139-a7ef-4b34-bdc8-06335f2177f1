package com.icetea.lotus.controller.system;

import com.icetea.lotus.service.system.ExtendedMemberBonusService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping("/system/member-bonus")
@Slf4j
@RequiredArgsConstructor
public class MemberBonusController {

    private final ExtendedMemberBonusService extendedMemberBonusService;

    /**
     * Find all condition message result.
     *
     * @param memberId the member id
     * @param phone    the phone
     * @param pageNo   the page no
     * @param pageSize the page size
     * @return the message result
     */
    @RequestMapping(value = "/page-query",method = RequestMethod.POST)
    @PreAuthorize("hasRole('system:member-bonus:info')")
    public MessageResult findAllCondition(@RequestParam(value = "memberId",required = false)Long memberId,
                                          @RequestParam(value = "phone",required = false)String phone,
                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize){

        return extendedMemberBonusService.findAllCondition(memberId, phone, pageNo, pageSize);
    }
}
