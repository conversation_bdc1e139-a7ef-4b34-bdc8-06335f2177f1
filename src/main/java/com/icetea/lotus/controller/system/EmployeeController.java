//package com.icetea.lotus.controller.system;
//
//import com.icetea.lotus.annotation.AccessLog;
//import com.icetea.lotus.constant.AdminModule;
//import com.icetea.lotus.constant.PageModel;
//import com.icetea.lotus.entity.spot.Admin;
//import com.icetea.lotus.service.system.EmployeeService;
//import com.icetea.lotus.util.BindingResultUtil;
//import com.icetea.lotus.util.MessageResult;
//import freemarker.template.TemplateException;
//import org.springframework.security.access.prepost.PreAuthorize;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Controller;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.validation.BindingResult;
//import org.springframework.web.bind.annotation.*;
//import jakarta.mail.MessagingException;
//import jakarta.servlet.http.HttpServletRequest;
//import org.springframework.web.client.RestTemplate;
//
//import java.io.IOException;
//import java.util.concurrent.CompletableFuture;
//
//
///**
// * The type Employee controller.
// */
//@Slf4j
//@Controller
//@RequestMapping("/system/employee")
//@RequiredArgsConstructor
//public class EmployeeController {
//    private final EmployeeService employeeService;
//
//    /**
//     * Admin login message result.
//     *
//     * @param username the username
//     * @param password the password
//     * @param captcha  the captcha
//     * @return the message result
//     */
//    @RequestMapping(value = "/login")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Determine the background login and enter the mobile phone verification code")
//    public MessageResult adminLogin(@RequestParam(value = "username", required = true) String username,
//                                    @RequestParam(value = "password", required = true) String password,
//                                    @RequestParam(value = "captcha", required = false) String captcha) {
//
//        return employeeService.adminLogin(username, password, captcha);
//    }
//
//    /**
//     * Submit login information
//     *
//     * @param username   the username
//     * @param password   the password
//     * @param phone      the phone
//     * @param code       the code
//     * @param rememberMe the remember me
//     * @param request    the request
//     * @return message result
//     */
//    @RequestMapping(value = "sign/in")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Submit the login information to the Admin")
//    public MessageResult doLogin(@SessionAttribute("username") String username,
//                                 @SessionAttribute("password") String password,
//                                 @SessionAttribute("phone") String phone, String code,
//                                 @RequestParam(value = "rememberMe", defaultValue = "true") boolean rememberMe,
//                                 HttpServletRequest request) {
//
//        return employeeService.doLogin(username, password, phone, code, rememberMe, request);
//    }
//
//    /**
//     * Send an email
//     *
//     * @param email   the email
//     * @param msg     the msg
//     * @param subject the subject
//     * @throws MessagingException
//     * @throws IOException
//     * @throws TemplateException
//     */
//    @Async
//    public CompletableFuture<MessageResult> sendEmailMsg(String email, String msg, String subject) {
//        return employeeService.sendEmailMsg(email, msg, subject);
//    }
//
//    /**
//     * Valiate phone code message result.
//     *
//     * @param request the request
//     * @return the message result
//     */
//    @RequestMapping(value = "/check")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Determine the background login and enter the mobile phone verification code")
//    public MessageResult valiatePhoneCode(HttpServletRequest request) {
//
//        return employeeService.valiatePhoneCode(request);
//    }
//
//
//    /**
//     * Log out
//     *
//     * @return message result
//     */
//    @RequestMapping(value = "logout")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Log out")
//    public MessageResult logout(RestTemplate restTemplate,
//                                @RequestParam("refresh_token") String refreshToken
//    ) {
//        return employeeService.logout(restTemplate, refreshToken);
//    }
//
//    /**
//     * Create or change backend users
//     *
//     * @param admin         the admin
//     * @param departmentId  the department id
//     * @param bindingResult the binding result
//     * @return message result
//     */
//    @PreAuthorize("hasRole('system:employee:merge')")
//    @RequestMapping(value = "/merge")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Create or change background users")
//    @Transactional(rollbackFor = Exception.class)
//    public MessageResult addAdmin(Admin admin,
//                                  @RequestParam("departmentId") Long departmentId,
//                                  BindingResult bindingResult) {
//        MessageResult result = BindingResultUtil.validate(bindingResult);
//        if (result != null) {
//            return result;
//        }
//        return employeeService.addAdmin(admin, departmentId);
//    }
//
//    /**
//     * Find all admin user message result.
//     *
//     * @param pageModel the page model
//     * @param searchKey the search key
//     * @return the message result
//     */
//    @ResponseBody
//    @PreAuthorize("hasRole('system:employee:page-query')")
//    @PostMapping("page-query")
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Paginate to find the admin user in the background")
//    public MessageResult findAllAdminUser(
//            PageModel pageModel,
//            @RequestParam(value = "searchKey", defaultValue = "") String searchKey) {
//
//        return employeeService.findAllAdminUser(pageModel, searchKey);
//
//    }
//
//    /**
//     * Update password message result.
//     *
//     * @param id           the id
//     * @param lastPassword the last password
//     * @param newPassword  the new password
//     * @return the message result
//     */
//    @PreAuthorize("hasRole('system:employee:update-password')")
//    @PostMapping("update-password")
//    @ResponseBody
//    public MessageResult updatePassword(Long id, String lastPassword, String newPassword) {
//
//        return employeeService.updatePassword(id, lastPassword, newPassword);
//
//    }
//
//    /**
//     * Reset password message result.
//     *
//     * @param id the id
//     * @return the message result
//     */
//    @PostMapping("reset-password")
//    @ResponseBody
//    public MessageResult resetPassword(Long id) {
//
//        return employeeService.resetPassword(id);
//    }
//
//    /**
//     * Admin Information
//     *
//     * @param id the id
//     * @return message result
//     */
//    @PreAuthorize("hasRole('system:employee:detail')")
//    @RequestMapping(value = "/detail")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Details of the Admin user in the background")
//    public MessageResult adminDetail(Long id) {
//        return employeeService.adminDetail(id);
//
//    }
//
//    /**
//     * Admin Information
//     *
//     * @param ids the ids
//     * @return message result
//     */
//    @PreAuthorize("hasRole('system:employee:deletes')")
//    @RequestMapping(value = "/deletes")
//    @ResponseBody
//    @AccessLog(module = AdminModule.SYSTEM, operation = "Details of the Admin user in the background")
//    public MessageResult deletes(Long[] ids) {
//        return employeeService.deletes(ids);
//    }
//
//
//    @RequestMapping(value = "/refresh-token")
//    @ResponseBody
//    public MessageResult refreshToken(@RequestParam("refresh_token") String refreshToken) {
//        log.info("RefreshToken==> refreshToken = {}", refreshToken);
//        return employeeService.refreshToken(refreshToken);
//    }
//}
//
