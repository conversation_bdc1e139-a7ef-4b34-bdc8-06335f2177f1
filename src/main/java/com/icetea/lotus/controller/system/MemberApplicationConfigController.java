package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.entity.spot.MemberApplicationConfig;
import com.icetea.lotus.service.system.ExtendedMemberApplicationConfigService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Member application config controller.
 */
@RestController
@RequestMapping("/system/member-application-config")
@RequiredArgsConstructor
public class MemberApplicationConfigController {

    private final ExtendedMemberApplicationConfigService extendedMemberApplicationConfigService ;

    /**
     * Merge message result.
     *
     * @param memberApplicationConfig the member application config
     * @param bindingResult           the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:member-application-config:merge')")
    @PostMapping("merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Real-name authentication configuration modification")
    public MessageResult merge(@Valid MemberApplicationConfig memberApplicationConfig, BindingResult bindingResult){
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if(result!=null) {
            return result ;
        }

        return extendedMemberApplicationConfigService.merge(memberApplicationConfig);
    }

    /**
     * Query message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('system:member-application-config:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Real-name authentication configuration details")
    public MessageResult query(){
        return extendedMemberApplicationConfigService.query();
    }
}
