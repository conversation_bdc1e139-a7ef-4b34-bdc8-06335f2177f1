package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Department;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedDepartmentService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * The type Department controller.
 */
@RestController
@RequestMapping(value = "/system/department")
public class DepartmentController extends BaseAdminController {

    private final ExtendedDepartmentService extendedDepartmentService;

    public DepartmentController(BaseAdminService baseAdminService, ExtendedDepartmentService extendedDepartmentService) {
        super(baseAdminService);
        this.extendedDepartmentService = extendedDepartmentService;
    }

    /**
     * Create or update a department
     *
     * @param department    the department
     * @param bindingResult the binding result
     * @return message result
     */
    @PreAuthorize("hasRole('system:department:merge')")
    @RequestMapping("merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create or update department")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult save(@Valid Department department, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        return extendedDepartmentService.save(department);
    }

    /**
     * Department details
     *
     * @param departmentId the department id
     * @return message result
     */
    @PreAuthorize("hasRole('system:department:detail')")
    @RequestMapping("detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Department Department details")
    public MessageResult detail(Long departmentId) {
        return extendedDepartmentService.detail(departmentId);

    }

    /**
     * All departments
     *
     * @param pageModel the page model
     * @return message result
     */
    @PreAuthorize("hasRole('system:department:all')")
    @RequestMapping("all")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Departments of all departments")
    public MessageResult allDepartment(PageModel pageModel) {
        return extendedDepartmentService.allDepartmrnt(pageModel);
    }

    /**
     * Deletes message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:department:deletes')")
    @RequestMapping("deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Batch deletion department")
    public MessageResult deletes(@RequestParam(value = "id") Long id) {
        return extendedDepartmentService.deletes(id);
    }

}
