package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.service.system.ExtendedStatisticsService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The type Statistics controller.
 *
 * <AUTHOR> @description
 * @date 2019 /1/8 16:19
 */
@RestController
@RequestMapping("/system/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final ExtendedStatisticsService extendedStatisticsService;

    /**
     * Member statistics message result.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @return the message result
     * <AUTHOR> @description Query new user curve
     * @date 2019 /1/8 16:25
     */
    @PreAuthorize("hasRole('system:statistics:member-statistics')")
    @PostMapping("member-statistics")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Query new users curve")
    public MessageResult memberStatistics(String startTime, String endTime) {

        return extendedStatisticsService.memberStatistics(startTime, endTime);
    }

    /**
     * Delegation statistics message result.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @return the message result
     * <AUTHOR> @description Delegate curve
     * @date 2019 /1/9 13:52
     */
    @PreAuthorize("hasRole('system:statistics:delegation-statistics')")
    @PostMapping("delegation-statistics")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Delegation curve")
    public MessageResult delegationStatistics(String startTime, String endTime) {

        return extendedStatisticsService.delegationStatistics(startTime, endTime);
    }

    /**
     * Order statistics message result.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @return the message result
     * <AUTHOR> @description Trading volume Order volume
     * @date 2019 /1/9 14:50
     */
    @PreAuthorize("hasRole('system:statistics:order-statistics')")
    @PostMapping("order-statistics")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Trading volume Order volume")
    public MessageResult orderStatistics(String startTime, String endTime) {

        return extendedStatisticsService.orderStatistics(startTime, endTime);
    }

    /**
     * Dashboard message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('system:statistics:dashboard')")
    @PostMapping("dashboard")
    @AccessLog(module = AdminModule.SYSTEM, operation = "dashboard")
    public MessageResult dashboard() {

        return extendedStatisticsService.dashboard();
    }

    /*@PreAuthorize("hasRole('system:statistics:order-rate')")
    @PostMapping("order-rate")
    @AccessLog(module = AdminModule.SYSTEM, operation = "订单手续费统计")
    public Pagenation<OrderDetailAggregation> detail(
                     @RequestParam(required = false)PageParam pageParam,
                     @RequestParam(value = "memberId",defaultValue = "0")Long memberId,
                     @RequestParam(value = "orderType",required = false)OrderTypeEnum orderType,
                     @RequestParam(value = "coinName",required = false)String coinName
                                                     ){
        return orderDetailAggregationService.getDetail(pageParam,memberId,coinName,orderType);
    }*/
}


