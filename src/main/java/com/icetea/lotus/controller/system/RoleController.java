package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedSysRoleService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Role controller.
 *
 * <AUTHOR> @date December 20, 2020
 */
@RestController
@RequestMapping(value = "system/role")
public class RoleController extends BaseAdminController {

    private final ExtendedSysRoleService extendedSysRoleService;

    public RoleController(BaseAdminService baseAdminService, ExtendedSysRoleService extendedSysRoleService) {
        super(baseAdminService);
        this.extendedSysRoleService = extendedSysRoleService;
    }

    /**
     * Create or modify roles
     *
     * @param sysRole       the sys role
     * @param bindingResult the binding result
     * @return message result
     */


    @PreAuthorize("hasRole('system:role:merge')")
    @RequestMapping("merge")
    @Transactional(rollbackFor = Exception.class)
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create or modify role SysRole")
    public MessageResult mergeRole(@Valid SysRole sysRole, BindingResult bindingResult) {

        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        return extendedSysRoleService.mergeRole(sysRole);

    }

    /**
     * All permission trees
     *
     * @return message result
     */
    @PreAuthorize("hasRole('system:role:permission:all')")
    @RequestMapping("permission/all")
    @AccessLog(module = AdminModule.SYSTEM, operation = "All permissions tree Menu")
    public MessageResult allMenu() {

        return extendedSysRoleService.allMenu();
    }

    /**
     * Permissions that the role has
     *
     * @param roleId the role id
     * @return message result
     */
    @PreAuthorize("hasRole('system:role:permission')")
    @RequestMapping("permission")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Permissions that the role has Menu")
    public MessageResult roleAllPermission(Long roleId) {

        return extendedSysRoleService.roleAllPermission(roleId);
    }

    /**
     * Change role permissions
     *
     * @param roleId
     * @param permissionId
     * @return
     */
    // @PreAuthorize("hasRole('system:role:permission:update')")
   /* @RequestMapping("permission/update")
    @Transactional(rollbackFor = Exception.class)
    @AccessLog(module = AdminModule.SYSTEM, operation = "更改角色拥有的权限Menu")
    public MessageResult updateRolePermission(Long roleId, Long[] permissionId) {
        SysRole sysRole = sysRoleService.findOne(roleId);
        if (permissionId != null) {
            List<SysPermission> list = Arrays.stream(permissionId)
                    .map(x -> sysPermissionService.findOne(x))
                    .collect(Collectors.toList());
            sysRole.setPermissions(list);
        } else {
            sysRole.setPermissions(null);
        }
        return success("操作成功");
    }*/

    /**
     * All roles
     *
     * @param pageModel the page model
     * @return all role
     */
    @PreAuthorize("hasRole('system:role:all')")
    @RequestMapping("all")
    @AccessLog(module = AdminModule.SYSTEM, operation = "All roles SysRole")
    public MessageResult getAllRole(PageModel pageModel) {

        return extendedSysRoleService.getAllRole(pageModel);
    }

    /**
     * Delete roles
     *
     * @param id the id
     * @return message result
     */
    @PreAuthorize("hasRole('system:role:deletes')")
    @RequestMapping("deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Delete role SysRole")
    public MessageResult deletes(Long id) {

        return extendedSysRoleService.deletes(id);
    }


}
