package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.model.exchange.ExchangeCoinRateRequest;
import com.icetea.lotus.model.exchange.RobotConfigRequest;
import com.icetea.lotus.model.screen.ExchangeCoinScreen;
import com.icetea.lotus.service.exchange.ExtendedExchangeCoinService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import javax.validation.Valid;
import java.math.BigDecimal;


@RestController
@RequestMapping("exchange/exchange-coin")
@RequiredArgsConstructor
public class ExchangeCoinController {

    private final ExtendedExchangeCoinService extendedExchangeCoinService;


    /**
     * Merge or add a currency trading pair.
     *
     * @param exchangeCoin The trading pair information to be merged or added.
     * @return MessageResult containing the result of the operation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:merge')")
    @PostMapping("merge")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Added currency trading pair exchangeCoin")
    public MessageResult exchangeCoinList(
            @Valid ExchangeCoin exchangeCoin) {
        return extendedExchangeCoinService.exchangeCoinList(exchangeCoin);

    }

    /**
     * Query paginated list of exchange coins with optional filtering.
     *
     * @param pageModel The pagination and sorting information.
     * @param screen    The filter criteria for the exchange coins.
     * @return MessageResult containing the filtered paginated list.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Search for transaction fees for currency transactions on pages exchangeCoin")
    public MessageResult exchangeCoinList(PageModel pageModel, ExchangeCoinScreen screen) {

        return extendedExchangeCoinService.exchangeCoinList(pageModel, screen);
    }

    /**
     * Get detail information of a specific exchange coin.
     *
     * @param symbol The symbol of the exchange coin.
     * @return MessageResult containing the exchange coin details.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Coin transaction pair exchangeCoin details")
    public MessageResult detail(
            @RequestParam(value = "symbol") String symbol) {
        return extendedExchangeCoinService.detail(symbol);
    }

    /**
     * Delete multiple exchange coin entries by their IDs.
     *
     * @param ids Array of exchange coin IDs to be deleted.
     * @return MessageResult indicating the success or failure of the deletion process.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:deletes')")
    @PostMapping("deletes")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Coin transaction pair exchangeCoin delete")
    public MessageResult deletes(
            @RequestParam(value = "ids") String[] ids) {
        return extendedExchangeCoinService.deletes(ids);
    }

    /**
     * Modify the exchange rate configuration of a specific exchange coin.
     *
     * @param exchangeCoinRateRequest Request object containing the new rate details.
     * @param admin                   The admin performing this operation, obtained from the session.
     * @return MessageResult indicating the result of the operation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:alter-rate')")
    @PostMapping("alter-rate")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Modify currency transaction pair exchangeCoin")
    public MessageResult alterExchangeCoinRate(
            @ModelAttribute ExchangeCoinRateRequest exchangeCoinRateRequest,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {

        return extendedExchangeCoinService.alterExchangeCoinRate(exchangeCoinRateRequest, admin);

    }

    /**
     * Start the trading engine for a given exchange coin.
     *
     * @param symbol   The symbol of the exchange coin.
     * @param password Admin's operation password.
     * @param admin    The admin performing the operation.
     * @return MessageResult indicating whether the trading engine was successfully started.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:start-trader')")
    @PostMapping("start-trader")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Start the trading engine")
    public MessageResult startExchangeCoinEngine(
            @RequestParam("symbol") String symbol,
            @RequestParam(value = "password") String password,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.startExchangeCoinEngine(symbol, password, admin);

    }

    /**
     * Stop the trading engine for a given exchange coin.
     *
     * @param symbol   The symbol of the exchange coin.
     * @param password Admin's operation password.
     * @param admin    The admin performing the operation.
     * @return MessageResult indicating whether the trading engine was successfully stopped.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:stop-trader')")
    @PostMapping("stop-trader")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Stop the trading engine")
    public MessageResult stopExchangeCoinEngine(
            @RequestParam("symbol") String symbol,
            @RequestParam(value = "password") String password,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.stopExchangeCoinEngine(symbol, password, admin);

    }

    /**
     * Reset the trading engine for a specific exchange coin.
     *
     * @param symbol   The symbol of the exchange coin.
     * @param password Admin's operation password.
     * @param admin    The admin performing the reset.
     * @return MessageResult indicating the result of the reset operation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:reset-trader')")
    @PostMapping("reset-trader")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Reset the trading engine")
    public MessageResult resetExchangeCoinEngine(
            @RequestParam("symbol") String symbol,
            @RequestParam(value = "password") String password,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.resetExchangeCoinEngine(symbol, password, admin);

    }

    /**
     * Export exchange coin transaction fee data to an Excel file.
     *
     * @param request  HTTP request object.
     * @param response HTTP response used to return the Excel file.
     * @return MessageResult indicating the export status.
     * @throws Exception if an error occurs during the export process.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Export currency transaction fee exchangeCoin Excel")
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return extendedExchangeCoinService.outExcel(request, response);

    }

    /**
     * Get a list of all base symbol units available for exchange coins.
     *
     * @return MessageResult containing a list of base symbol units.
     */
    @PostMapping("all-base-symbol-units")
    public MessageResult getAllBaseSymbolUnits() {
        return extendedExchangeCoinService.getAllBaseSymbolUnits();

    }

    /**
     * Get all quote symbols for a specific base symbol.
     *
     * @param baseSymbol The base symbol to filter quote symbols.
     * @return MessageResult containing quote symbol units.
     */
    @PostMapping("all-coin-symbol-units")
    public MessageResult getAllCoinSymbolUnits(@RequestParam("baseSymbol") String baseSymbol) {
        return extendedExchangeCoinService.getAllCoinSymbolUnits(baseSymbol);
    }

    /**
     * Cancel all open orders for a specific exchange coin.
     *
     * @param symbol   The symbol of the exchange coin.
     * @param password Admin's operation password.
     * @param admin    The admin performing the cancel operation.
     * @return MessageResult indicating the result of the cancellation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:cancel-all-order')")
    @PostMapping("cancel-all-order")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Revoke a transaction to all delegations to exchangeCoin")
    public MessageResult cancelAllOrderBySymbol(
            @RequestParam("symbol") String symbol,
            @RequestParam(value = "password") String password,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.cancelAllOrderBySymbol(symbol, password, admin);
    }

    /**
     * View trading overview details for a specific exchange coin.
     *
     * @param symbol The symbol of the exchange coin.
     * @return MessageResult containing overview details of the exchange coin.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:exchange-overview')")
    @PostMapping("exchange-overview")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "View transaction pair trading details")
    public MessageResult overviewExchangeCoin(@RequestParam("symbol") String symbol) {
        return extendedExchangeCoinService.overviewExchangeCoin(symbol);

    }

    /**
     * Retrieve current robot configuration for a specific exchange coin.
     *
     * @param symbol The symbol of the exchange coin.
     * @return MessageResult containing the robot configuration.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:robot-config')")
    @RequestMapping("robot-config")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "View transaction pair robot parameters")
    public MessageResult getRobotConfig(@RequestParam("symbol") String symbol) {

        return extendedExchangeCoinService.getRobotConfig(symbol);
    }

    /**
     * Update the robot configuration for a specific exchange coin.
     *
     * @param robotConfigRequest The new robot configuration data.
     * @param admin              The admin performing the update.
     * @return MessageResult indicating the result of the update.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:alter-robot-config')")
    @PostMapping("alter-robot-config")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Modify transaction pair robot parameters")
    public MessageResult alterRobotConfig(
            @ModelAttribute RobotConfigRequest robotConfigRequest,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.alterRobotConfig(robotConfigRequest, admin);
    }

    /**
     * Create new robot configuration for a specific exchange coin.
     *
     * @param robotConfigRequest The robot configuration data to be created.
     * @param admin              The admin performing the operation.
     * @return MessageResult indicating whether the configuration was successfully created.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:create-robot-config')")
    @PostMapping("create-robot-config")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Modify transaction pair robot parameters")
    public MessageResult createRobotConfig(
            @ModelAttribute RobotConfigRequest robotConfigRequest,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return extendedExchangeCoinService.createRobotConfig(robotConfigRequest, admin);
    }

    /**
     * Save new K-line (market trend) data for a robot associated with a symbol.
     *
     * @param symbol       The trading symbol.
     * @param kdate        The date of the K-line data.
     * @param kline        The K-line data string.
     * @param pricePencent The percentage to adjust the price in K-line.
     * @return MessageResult indicating the success of the operation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:save-robot-kline')")
    @PostMapping("save-robot-kline")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Save the robot market trend line")
    public MessageResult createRobotKlineData(@RequestParam("symbol") String symbol,
                                              @RequestParam("kdate") String kdate,
                                              @RequestParam("kline") String kline,
                                              @RequestParam("pricePencent") Integer pricePencent) {
        return extendedExchangeCoinService.createRobotKlineData(symbol, kdate, kline, pricePencent);

    }


    /**
     * Save a robot-controlled flow trend for a specific exchange coin.
     *
     * @param symbol      The trading symbol.
     * @param pair        The quote symbol (e.g., base/quote).
     * @param flowPercent The percentage used for flow control.
     * @return MessageResult indicating the result of the operation.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:save-robot-flow')")
    @PostMapping("save-robot-flow")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Setting up a follow-up disk control trend")
    public MessageResult createRobotFlow(@RequestParam("symbol") String symbol,
                                         @RequestParam("pair") String pair,
                                         @RequestParam("flowPercent") BigDecimal flowPercent) {
        return extendedExchangeCoinService.createRobotFlow(symbol, pair, flowPercent);
    }

    /**
     * Retrieve a list of all custom-configured trading pairs under robot control.
     *
     * @return MessageResult containing a list of trading pairs with custom control.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:custom-coin-list')")
    @PostMapping("custom-coin-list")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Get a list of all control trading pairs")
    public MessageResult customRobotCoinList() {
        return extendedExchangeCoinService.customRobotCoinList();
    }

    /**
     * Retrieve the list of robot K-line trend data for a specific symbol and date.
     *
     * @param symbol The trading symbol.
     * @param kdate  The date for which K-line data is requested.
     * @return MessageResult containing the list of K-line trend data.
     */
    @PreAuthorize("hasRole('exchange:exchange-coin:robot-kline-list')")
    @PostMapping("robot-kline-list")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Get a list of trend lines")
    public MessageResult robotKlineDataList(@RequestParam("symbol") String symbol, @RequestParam("kdate") String kdate) {
        return extendedExchangeCoinService.robotKlineDataList(symbol, kdate);
    }


}
