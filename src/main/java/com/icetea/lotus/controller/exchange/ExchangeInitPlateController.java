package com.icetea.lotus.controller.exchange;


import com.icetea.lotus.entity.spot.InitPlate;
import com.icetea.lotus.service.exchange.ExchangeInitPlateService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("exchange_init")
@Slf4j
@RequiredArgsConstructor
public class ExchangeInitPlateController {

    private final ExchangeInitPlateService exchangeInitPlateService;

    /**
     * Query paginated list of initialized exchange plates with optional symbol filtering.
     *
     * @param pageNo The current page number (starting from 1).
     * @param pageSize The number of records per page.
     * @param symbol (Optional) The trading pair symbol to filter results.
     * @return MessageResult containing the filtered paginated list of exchange plates.
     * @throws Exception if any error occurs during the query process.
     */
    @PreAuthorize("hasRole('exchange:init-plate:query')")
    @PostMapping("query")
    public MessageResult queryExchangeInitPlate(@RequestParam int pageNo,
                                                @RequestParam int pageSize,
                                                @RequestParam(required = false) String symbol) throws Exception {
        return exchangeInitPlateService.queryExchangeInitPlate(pageNo, pageSize, symbol);

    }

    /**
     * Get the detailed information of a specific initialized exchange plate by ID.
     *
     * @param id The ID of the exchange plate to retrieve.
     * @return MessageResult containing the detailed data of the exchange plate.
     * @throws Exception if the plate with the specified ID does not exist or retrieval fails.
     */
    @PreAuthorize("hasRole('exchange:init-plate:detail')")
    @GetMapping("detail/{id}")
    public MessageResult queryDetailExchangeInitPlate(@PathVariable("id") long id) throws Exception {
        return exchangeInitPlateService.queryDetailExchangeInitPlate(id);

    }

    /**
     * Delete a specific initialized exchange plate by ID.
     *
     * @param id The ID of the exchange plate to delete.
     * @return MessageResult indicating success or failure of the delete operation.
     * @throws Exception if deletion fails or the plate does not exist.
     */
    @PreAuthorize("hasRole('exchange:init-plate:delete')")
    @GetMapping("delete/{id}")
    public MessageResult deleteExchangeInitPlate(@PathVariable("id") long id) throws Exception {
        return exchangeInitPlateService.deleteExchangeInitPlate(id);
    }

    /**
     * Update an existing initialized exchange plate.
     *
     * @param initPlate The updated InitPlate object containing new values.
     * @return MessageResult indicating success or failure of the update operation.
     * @throws Exception if update fails or validation errors occur.
     */
    @PreAuthorize("hasRole('exchange:init-plate:update')")
    @PostMapping("update")
    public MessageResult updateExchangeInitPlate(InitPlate initPlate) throws Exception {
        return exchangeInitPlateService.updateExchangeInitPlate(initPlate);
    }


    /**
     * Add a new initialized exchange plate.
     *
     * @param initPlate The InitPlate object containing information for the new plate.
     * @return MessageResult indicating success or failure of the add operation.
     * @throws Exception if the creation fails due to validation or system errors.
     */
    @PreAuthorize("hasRole('exchange:init-plate:add')")
    @PostMapping("add")
    public MessageResult addExchangeInitPlate(InitPlate initPlate) throws Exception {
        return exchangeInitPlateService.addExchangeInitPlate(initPlate);
    }

}
