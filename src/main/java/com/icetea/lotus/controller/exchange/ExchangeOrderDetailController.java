package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.service.OrderDetailAggregationService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.exchange.ExtendedExchangeOrderService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/exchange/exchange-order-detail")
@Slf4j
public class ExchangeOrderDetailController extends BaseAdminController {

    private final ExtendedExchangeOrderService extendedExchangeOrderService;

    public ExchangeOrderDetailController(BaseAdminService baseAdminService, ExtendedExchangeOrderService extendedExchangeOrderService) {
        super(baseAdminService);
        this.extendedExchangeOrderService = extendedExchangeOrderService;
    }

    /**
     * Paginated query to retrieve exchange order details, optionally filtered by member ID.
     *
     * @param pageModel The pagination and sorting information.
     * @param memberId  (Optional) The ID of the member to filter order details.
     * @return MessageResult containing paginated list of exchange order details.
     */
    @PostMapping("/page-query")
    @ResponseBody
    public MessageResult getOrderDetails(
            PageModel pageModel,
            @RequestParam(value = "memberId", required = false) Long memberId
    ) {
        return extendedExchangeOrderService.getOrderDetails(pageModel, memberId);

    }
}
