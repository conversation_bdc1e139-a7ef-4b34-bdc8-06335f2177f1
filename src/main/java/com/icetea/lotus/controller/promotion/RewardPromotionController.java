package com.icetea.lotus.controller.promotion;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.*;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.RewardPromotionSetting;
import com.icetea.lotus.service.promotion.ExtendedRewardPromotionService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RestController
@RequestMapping("promotion/reward")
@RequiredArgsConstructor
public class RewardPromotionController {

    private final ExtendedRewardPromotionService extendedRewardPromotionService;

    /**
     * Merge message result.
     *
     * @param setting  the setting
     * @param admin    the admin
     * @param unit     the unit
     * @param password the password
     * @return the message result
     */
    @PreAuthorize("hasRole('promotion:reward:merge')")
    @PostMapping("merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create and modify invitation reward settings")
    public MessageResult merge(@Valid RewardPromotionSetting setting, @SessionAttribute(SysConstant.SESSION_ADMIN)Admin admin
                                ,String unit, @RequestParam(value = "password") String password) {

        return extendedRewardPromotionService.merge(setting, admin, unit, password);

    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('promotion:reward:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Invitation Reward Settings Details")
    public MessageResult detail(@RequestParam("id")Long id) {
        return extendedRewardPromotionService.detail(id);
    }

    /**
     * Query all undisabled (judgment type conditions)
     * By default, it is in descending order of updatetime
     *
     * @param pageModel the page model
     * @param enable    the enable
     * @param type      the type
     * @return message result
     */
    @PreAuthorize("hasRole('promotion:reward:page-query')")
    @GetMapping("page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Pagination query invitation reward settings")
    public MessageResult pageQuery(
            PageModel pageModel,
            @RequestParam(value = "status", defaultValue = "1") BooleanEnum enable,
            @RequestParam(value = "type", required = false) PromotionRewardType type) {

        return extendedRewardPromotionService.pageQuery(pageModel, enable, type);
    }

    /**
     * Deletes message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('promotion:reward:deletes')")
    @DeleteMapping("deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Batch Delete Invitation Reward Settings")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult deletes(long[] ids) {
        return extendedRewardPromotionService.deletes(ids);
    }
}
