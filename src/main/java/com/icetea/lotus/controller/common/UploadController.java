package com.icetea.lotus.controller.common;


import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.common.UploadService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


/**
 * The type Upload controller.
 */
@Controller
@RequestMapping("/common/upload")
@RequiredArgsConstructor
public class UploadController extends BaseController {
    private final UploadService uploadService;

    /**
     * Upload oss image string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @RequestMapping(value = "/oss/image", method = RequestMethod.POST)
    @ResponseBody
    @AccessLog(module = AdminModule.COMMON, operation = "Upload oss pictures")
    public MessageResult uploadOssImage(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadOssImage(request, response, file);
    }

    /**
     * Upload local image string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @RequestMapping(value = "/local/image", method = RequestMethod.POST)
    @ResponseBody
    @AccessLog(module = AdminModule.COMMON, operation = "Upload local pictures")
    public MessageResult uploadLocalImage(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadLocalImage(request, response, file);
    }


    /**
     * Upload oss app string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @RequestMapping(value = "/oss/app", method = RequestMethod.POST)
    @ResponseBody
    @AccessLog(module = AdminModule.COMMON, operation = "Upload oss package")
    public MessageResult uploadOssApp(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadOssApp(request, response, file);
    }

    /**
     * Base 64 up load message result.
     *
     * @param base64Data the base 64 data
     * @return the message result
     */
    @PreAuthorize("hasRole('common:upload:oss:base64')")
    @RequestMapping(value = "/oss/base64", method = RequestMethod.POST)
    @ResponseBody
    @AccessLog(module = AdminModule.COMMON, operation = "base64 upload oss")
    public MessageResult base64UpLoad(@RequestParam String base64Data) {
        return uploadService.base64UpLoad(base64Data);
    }


}
