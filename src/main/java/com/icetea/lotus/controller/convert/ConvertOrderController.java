package com.icetea.lotus.controller.convert;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.ConvertOrderScreen;
import com.icetea.lotus.service.convert.ConvertService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * The type Convert order controller.
 */
@RestController
@Slf4j
@RequestMapping("/convert/order")
@RequiredArgsConstructor
public class ConvertOrderController {

    private final ConvertService convertService;


    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('convert:order:page-query')")
    @RequestMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Flash redeem order list")
    public MessageResult pageQuery(
            PageModel pageModel,
            ConvertOrderScreen screen) {
        return convertService.pageQueryOrder(pageModel, screen);
    }
}
