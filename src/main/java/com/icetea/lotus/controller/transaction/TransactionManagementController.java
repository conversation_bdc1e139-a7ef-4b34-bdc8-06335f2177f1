package com.icetea.lotus.controller.transaction;

import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.dto.AdminHistoryWithdrawRequest;
import com.icetea.lotus.service.transaction.TransactionManagementService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/management/transaction")
@RequiredArgsConstructor
public class TransactionManagementController extends BaseController {
    private final TransactionManagementService transactionManagementService;

    @GetMapping("/withdraw")
    public MessageResult getAllWithdrawHistory(@RequestBody AdminHistoryWithdrawRequest adminHistoryWithdrawRequest) {
        return success(transactionManagementService.getAllWithdrawHistory(adminHistoryWithdrawRequest));
    }
}
