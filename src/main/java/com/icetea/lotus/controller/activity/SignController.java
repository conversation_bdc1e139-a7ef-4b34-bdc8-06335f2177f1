package com.icetea.lotus.controller.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.model.create.SignCreate;
import com.icetea.lotus.model.screen.SignScreen;
import com.icetea.lotus.model.update.SignUpdate;
import com.icetea.lotus.service.activity.SignedService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * The type Sign controller.
 *
 * <AUTHOR> thanh
 * @Description :
 * @date 2019 /5/3
 */
@RestController
@RequestMapping("activity/sign")
@RequiredArgsConstructor
public class SignController {
    private final SignedService signService;

    /**
     * Create Sign
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @return message message result
     */
    @PreAuthorize("hasRole('activity:sign:post')")
    @PostMapping
    public MessageResult create(@Valid SignCreate model, BindingResult bindingResult) {
        return signService.create(model, bindingResult);
    }

    /**
     * update sign data
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @param id            the id
     * @return message message result
     */
    @PreAuthorize("hasRole('activity:sign:put')")
    @PutMapping("{id}")
    public MessageResult update(@Valid SignUpdate model, BindingResult bindingResult, @PathVariable("id") Long id) {
        return signService.update(model, bindingResult, id);
    }

    /**
     * Get Sign data
     *
     * @param screen    the screen
     * @param pageModel the page model
     * @return data message result
     */
    @PreAuthorize("hasRole('activity:sign:page-query')")
    @GetMapping("page-query")
    public MessageResult pageQuery(SignScreen screen, PageModel pageModel) {
        return signService.pageQuery(screen, pageModel);
    }

    /**
     * get detail activity
     *
     * @param id the id
     * @return data message result
     */
    @PreAuthorize("hasRole('activity:sign:id:get')")
    @GetMapping("{id}")
    public MessageResult detail(@PathVariable("id") Long id) {
        return signService.detail(id);
    }

    /**
     * update status activity
     *
     * @param id the id
     * @return message message result
     */
    @PreAuthorize("hasRole('activity:sign:id:early-closing')")
    @PatchMapping("{id}/early-closing")
    public MessageResult earlyClosing(@PathVariable("id") Long id) {
        return signService.earlyClosing(id);
    }

    /**
     * Check if there is any value that have status = UNDERWAY
     *
     * @return message message result
     */
    @GetMapping("has-underway")
    public MessageResult is() {
        return signService.is();
    }

}