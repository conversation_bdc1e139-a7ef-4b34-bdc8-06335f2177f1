//package com.icetea.lotus.controller.activity;
//
//import com.icetea.lotus.service.keyClockService.KeyClockService;
//import org.springframework.security.access.prepost.PreAuthorize;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * The type Keycloak login controller.
// */
//@RestController
//@RequestMapping("/auth")
//@RequiredArgsConstructor
//public class KeycloakLoginController {
//
//    private final KeyClockService keyClockService;
//
//    /**
//     * Login response entity.
//     *
//     * @param username the username
//     * @param password the password
//     * @return the response entity
//     */
//    @PostMapping("/login")
//    // Updated RestTemplate request
//    public ResponseEntity<?> login(@RequestParam String username, @RequestParam String password) {
//        return keyClockService.login(username, password);
//    }
//
//
//    /**
//     * Gets customers.
//     *
//     * @param pageNo the page no
//     * @return the customers
//     */
//    @PreAuthorize("hasRole('admin')")
//    @GetMapping("/customers")
//    public Object getCustomers(int pageNo) {
//        return keyClockService.getCustomers(pageNo);
//    }
//
//}