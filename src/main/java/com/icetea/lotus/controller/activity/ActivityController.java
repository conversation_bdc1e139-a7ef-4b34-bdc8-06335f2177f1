package com.icetea.lotus.controller.activity;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Activity;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.model.ActivityRequest;
import com.icetea.lotus.service.activity.ActivitiesService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import javax.validation.Valid;
import java.math.BigDecimal;


/**
 * The type Activity controller.
 */
@RestController
@RequestMapping("activity/activity")
@RequiredArgsConstructor
public class ActivityController {

    private final ActivitiesService activityService;


    /**
     * Get data from activity
     *
     * @param pageModel : PageModel
     * @return : Data of activity
     */
    @PreAuthorize("hasRole('activity:activity:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "View activity list activity")
    public MessageResult activityList(PageModel pageModel, String unit) {
        return activityService.activityList(pageModel, unit);
    }

    /**
     * Get data from activity with type and step
     *
     * @param type : Activity type
     * @param step : Activity step
     * @return : Data
     */
    @PreAuthorize("hasRole('activity:activity:locked-activity')")
    @PostMapping("locked-activity")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "View the list of lock-up activities")
    public MessageResult lockedActivityList(@RequestParam("type") Integer type,
                                            @RequestParam("step") Integer step) {
        return activityService.lockedActivityList(type, step);
    }

    /**
     * Add activity information
     *
     * @param activity : Activity table
     * @return : Data
     */
    @PreAuthorize("hasRole('activity:activity:add')")
    @PostMapping("add")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Added activity information")
    public MessageResult ExchangeCoinList(
            @Valid Activity activity) {
        return activityService.ExchangeCoinList(activity);
    }

    /**
     * Modify activity progress value
     *
     * @param id       :
     * @param progress :
     * @return :
     */
    @PreAuthorize("hasRole('activity:activity:modify-progress')")
    @PostMapping("modify-progress")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Modify activity progress Activity")
    public MessageResult alterActivity(
            @RequestParam("id") Long id,
            @RequestParam("progress") Integer progress) {
        return activityService.alterActivity(id, progress);
    }

    /**
     * Modify the activity to freeze total assets
     *
     * @param id           the id
     * @param freezeAmount the freeze amount
     * @return message result
     */
    @PreAuthorize("hasRole('activity:activity:modify-freezeamount')")
    @PostMapping("modify-freezeamount")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Modify activity freeze total assets Activity")
    public MessageResult alterActivityFreezeAmount(
            @RequestParam("id") Long id,
            @RequestParam("freezeAmount") BigDecimal freezeAmount) {
        return activityService.alterActivityFreezeAmount(id, freezeAmount);
    }

    /**
     * Modify the total number of transactions
     *
     * @param id           the id
     * @param tradedAmount the traded amount
     * @return message result
     */
    @PreAuthorize("hasRole('activity:activity:modify-tradedamount')")
    @PostMapping("modify-tradedamount")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Modify the total number of activities transactions Activity")
    public MessageResult alterActivityTradedAmount(
            @RequestParam("id") Long id,
            @RequestParam("tradedAmount") BigDecimal tradedAmount) {
        return activityService.alterActivityTradedAmount(id, tradedAmount);
    }

    /**
     * Alter activity message result.
     *
     * @param activityRequest the activity request
     * @param admin           the admin
     * @return the message result
     */
// Modify
    @PreAuthorize("hasRole('activity:activity:modify')")
    @PostMapping("modify")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Modify activity information")
    public MessageResult alterActivity(
//            @RequestParam("activityRequest")
            ActivityRequest activityRequest,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return activityService.alterActivity(activityRequest, admin);
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('activity:activity:detail')")
    @GetMapping("{id}/detail")
    public MessageResult detail(
            @PathVariable Long id) {
        return activityService.detail(id);
    }

    /**
     * Order list message result.
     *
     * @param aid the aid
     * @return the message result
     */
    @PreAuthorize("hasRole('activity:activity:orderlist')")
    @GetMapping("{aid}/orderlist")
    public MessageResult orderList(
            @PathVariable Long aid) {
        return activityService.orderList(aid);
    }

    /**
     * Distribute active coins
     *
     * @param oid the oid
     * @return message result
     */
    @PreAuthorize("hasRole('activity:activity:distribute')")
    @PostMapping("distribute")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "Distribute activity coins")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult distribute(@RequestParam("oid") Long oid) {
        return activityService.distribute(oid);
    }

    /**
     * Administrator manually locks up user assets
     *
     * @param memberId   the member id
     * @param activityId the activity id
     * @param unit       the unit
     * @param amount     the amount
     * @return message result
     */
    @PreAuthorize("hasRole('activity:activity:lock-member-coin')")
    @PostMapping("lock-member-coin")
    @AccessLog(module = AdminModule.ACTIVITY, operation = "View activity list activity")
    public MessageResult lockMemberCoin(@RequestParam("memberId") Long memberId,
                                        @RequestParam("activityId") Long activityId,
                                        @RequestParam("unit") String unit,
                                        @RequestParam("amount") BigDecimal amount) {
        return activityService.lockMemberCoin(memberId, activityId, unit, amount);
    }
}
