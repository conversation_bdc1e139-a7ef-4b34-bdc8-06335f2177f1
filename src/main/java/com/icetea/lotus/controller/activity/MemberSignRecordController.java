package com.icetea.lotus.controller.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.model.screen.MemberSignRecordScreen;
import com.icetea.lotus.service.activity.MemberSignRecordedService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * The type Member sign record controler.
 *
 * <AUTHOR> thanhnv
 * @Description : Member check-in history
 * @date 2019 /5/4
 */
@RestController
@RequestMapping("activity/member-sign-record")

public class MemberSignRecordController extends BaseAdminController {

    private final MemberSignRecordedService memberSignRecordedService;

    public MemberSignRecordController(BaseAdminService baseAdminService, MemberSignRecordedService memberSignRecordedService) {
        super(baseAdminService);
        this.memberSignRecordedService = memberSignRecordedService;
    }

    /**
     * Page query message result.
     *
     * @param screen    the screen
     * @param pageModel the page model
     * @return message result
     */
    @PreAuthorize("hasRole('activity:member-sign-record:page-query')")
    @GetMapping("page-query")
    public MessageResult pageQuery(MemberSignRecordScreen screen, PageModel pageModel) {
        return memberSignRecordedService.pageQuery(screen, pageModel);
    }
}
