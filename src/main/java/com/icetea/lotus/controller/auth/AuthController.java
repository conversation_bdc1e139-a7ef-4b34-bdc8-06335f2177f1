/*
 * Copyright(C) 2025
 * LoginController.java, July 30,2025
 * thanhnv
 */
package com.icetea.lotus.controller.auth;

import com.aliyuncs.ram.model.v20150501.ChangePasswordRequest;
import com.icetea.lotus.dto.RegisterRequest;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.service.auth.AuthService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Admin auth controller
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    private final AuthService authService;

    /**
     * Login
     * @param loginRequest data request
     * @param request HttpServletRequest
     * @return message
     */
    @PostMapping("/login")
    public MessageResult login(@RequestBody LoginRequest loginRequest , HttpServletRequest request) {
        return authService.login(loginRequest,request);
    }

    /**
     * Change password
     * @param changePasswordRequest data request
     * @param request HttpServletRequest
     * @return message
     */
    @PutMapping("/change-password")
    public MessageResult changePassword(@RequestBody ChangePasswordRequest changePasswordRequest , HttpServletRequest request) {
        return authService.changePassword(changePasswordRequest,request);
    }

    /**
     * Log out
     * @param request HttpServletRequest
     * @return string
     */
    @PostMapping("/logout")
    public ResponseEntity<String> logout(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            authService.blacklist(token);
        }
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok("Logout successful");
    }

    /**
     * register api
     * @param registerRequest data request
     * @return message
     */
    @PostMapping("/register")
    public String register( @RequestBody RegisterRequest registerRequest) {
        return authService.register(registerRequest);
    }
}
