package com.icetea.lotus.controller.member;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.service.member.ExtendedInviteManagementService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vo.InviteManagementVO;
import com.icetea.lotus.vo.MemberInviteStasticVO;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;


@RestController
@Slf4j
@RequestMapping("invite/management")
@RequiredArgsConstructor
public class InviteManagementController {

    private final ExtendedInviteManagementService extendedInviteManagementService;

    /**
     * Retrieves all users based on the invite management criteria by default.
     *
     * @param inviteManagementVO The criteria for looking up the users.
     * @return MessageResult containing the result of the lookup.
     */
    @PreAuthorize("hasRole('invite:management:query')")
    @AccessLog(module = AdminModule.CMS, operation = "Invite management to query all users by default")
    @RequestMapping(value = "look", method = RequestMethod.POST)
    public MessageResult lookAll(@RequestBody InviteManagementVO inviteManagementVO) {
        log.info("Query all users by default lookAll ={}", inviteManagementVO);
        return extendedInviteManagementService.lookAll(inviteManagementVO);
    }

    /**
     * Queries users based on multiple conditions in the invite management system.
     *
     * @param inviteManagementVO The criteria for querying users.
     * @return MessageResult containing the results based on the provided conditions.
     */
    @AccessLog(module = AdminModule.CMS, operation = "Invite management multi-condition inquiry")
    @RequestMapping(value = "query", method = RequestMethod.POST)
    public MessageResult queryCondition(@RequestBody InviteManagementVO inviteManagementVO) {
        log.info("Query all users by default QueryCondition ={}", inviteManagementVO);
        return extendedInviteManagementService.queryCondition(inviteManagementVO);
    }

    /**
     * Retrieves first-level and second-level users based on the provided ID in invite management.
     *
     * @param inviteManagementVO The criteria containing the ID to query users.
     * @return MessageResult containing the first-level and second-level users based on the ID.
     */
    @AccessLog(module = AdminModule.CMS, operation = "Query Level 1 and Level 2 users based on id")
    @RequestMapping(value = "info", method = RequestMethod.POST)
    public MessageResult queryId(@RequestBody InviteManagementVO inviteManagementVO) {
        log.info("query first-level and second-level users based on id queryById={}", inviteManagementVO);
        return extendedInviteManagementService.queryId(inviteManagementVO);

    }

    /**
     * Retrieves the ranking list based on the invitation criteria.
     *
     * @param memberInviteStasticVO The criteria for querying the ranking list.
     * @return MessageResult containing the ranked list based on the provided criteria.
     */
    @PreAuthorize("hasRole('invite:management:rank')")
    @AccessLog(module = AdminModule.CMS, operation = "Invitation ranking criteria query")
    @RequestMapping(value = "rank", method = RequestMethod.POST)
    public MessageResult queryRankList(@RequestBody MemberInviteStasticVO memberInviteStasticVO) {
        return extendedInviteManagementService.queryRankList(memberInviteStasticVO);

    }

    /**
     * Updates the invitation rank information based on the provided parameters.
     *
     * @param id The ID of the invitation to be updated.
     * @param estimatedReward The estimated reward for the invitation.
     * @param extraReward The extra reward for the invitation.
     * @param levelOne The level one ranking for the invitation.
     * @param levelTwo The level two ranking for the invitation.
     * @return MessageResult indicating the result of the update operation.
     */
    @PreAuthorize("hasRole('invite:management:update-rank')")
    @AccessLog(module = AdminModule.CMS, operation = "Update invitation information")
    @PostMapping("update-rank")
    public MessageResult updateRank(@RequestParam("id") Long id,
    								@RequestParam("estimatedReward") BigDecimal estimatedReward,
    								@RequestParam("extraReward") BigDecimal extraReward,
    								@RequestParam("levelOne") Integer levelOne,
    								@RequestParam("levelTwo") Integer levelTwo) {
    	log.info(id.toString());
        return extendedInviteManagementService.updateRank(id, estimatedReward, extraReward, levelOne, levelTwo);
    }

    /**
     * Retrieves detailed information about an invitation rank based on the provided ID.
     * If no ID is provided, it defaults to fetching the rank information for ID 0.
     *
     * @param id The ID of the invitation rank to retrieve details for. Defaults to 0 if not provided.
     * @return MessageResult containing the invitation rank details.
     */
    @PreAuthorize("hasRole('invite:management:detail-rank')")
    @AccessLog(module = AdminModule.CMS, operation = "Invitation information details")
    @RequestMapping(value = "detail-rank", method = RequestMethod.POST)
    public MessageResult updateRank(@RequestParam(value = "id", defaultValue="0") Long id) {
        return extendedInviteManagementService.updateRank(id);
    }
}
