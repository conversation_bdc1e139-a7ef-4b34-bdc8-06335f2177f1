package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.service.finance.ExtendedTransferRecordService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/finance/transfer-record")
@RequiredArgsConstructor
public class TransferRecordController {
    private final ExtendedTransferRecordService transferRecordService;

    /**
     * Get paginated transfer records with filters.
     *
     * @param screen     filter conditions for transfer records
     * @param timezone   client timezone from request header
     * @return paginated result of transfer records
     */
    @GetMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Pagination query transfer record")
    public MessageResult pageQuery(
            @RequestBody TransferRecordDTO screen,
            @RequestHeader(value = "x-timezone") String timezone
            ) {

        log.info("TransferRecord PageQuery called with filters: {}, timezone: {}", screen, timezone);

        screen.setTimezone(timezone);
        return transferRecordService.pageQuery(screen);
    }
}

