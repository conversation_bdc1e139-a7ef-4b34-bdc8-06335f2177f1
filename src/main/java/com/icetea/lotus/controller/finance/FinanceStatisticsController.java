package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.TransactionTypeEnum;
import com.icetea.lotus.service.finance.FinanceStatisticsService;
import com.icetea.lotus.util.MessageResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;

@RestController
@RequestMapping("finance/statistics")
@RequiredArgsConstructor
public class FinanceStatisticsController {

    private final FinanceStatisticsService financeStatisticsService;

    /**
     * Retrieves total trading volume or turnover statistics for fiat/crypto trading types within a given date range.
     *
     * @param types Array of trading types to include in the statistics (e.g., "Fiat", "Crypto").
     * @param startDate The start date of the time range for the query (format: yyyy-MM-dd).
     * @param endDate The end date of the time range for the query (format: yyyy-MM-dd).
     * @param unit The currency unit used for the result (e.g., "USDT", "CNY").
     * @return MessageResult containing the total trading volume or turnover for the specified types and period.
     */
    @PostMapping("turnover-all")
    @AccessLog(module = AdminModule.FINANCE, operation = "Fiat/Crypto - Total Trading Volume/Total Turnover")
    public MessageResult getResult(
            String[] types,
            @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date startDate,
            @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date endDate,
            String unit) {

        return financeStatisticsService.getResult(types, startDate, endDate, unit);
    }

    /**
     * Retrieves total processing fee statistics for a specified transaction type and date range.
     *
     * @param type The transaction type to calculate fees for (e.g., OTC_NUM, WITHDRAW, EXCHANGE).
     * @param startDate The start date of the time range for the query (format: yyyy-MM-dd).
     * @param endDate The end date of the time range for the query (format: yyyy-MM-dd).
     * @param unit The currency unit used for the result (e.g., "USDT", "CNY").
     * @return MessageResult containing the total fees for the specified transaction type and period.
     */
    @PostMapping("fee")
    @AccessLog(module = AdminModule.FINANCE, operation = "Processing fee statistics Total[\"OTC_NUM\",\"WITHDRAW\",\"EXCHANGE\"]")
    public MessageResult getFee(TransactionTypeEnum type
            , @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date startDate
            , @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date endDate
            , String unit) {
        return financeStatisticsService.getFee(type, startDate, endDate,unit);


    }
    /**
     * Retrieves the total recharge or withdrawal amount for a specified transaction type and date range.
     *
     * @param type The transaction type to calculate amounts for (RECHARGE or WITHDRAW).
     * @param startDate The start date of the time range for the query (format: yyyy-MM-dd).
     * @param endDate The end date of the time range for the query (format: yyyy-MM-dd).
     * @return MessageResult containing the total amount for the specified transaction type and period.
     */
    @PostMapping("recharge-or-withdraw-amount")
    @AccessLog(module = AdminModule.FINANCE, operation = "Statistics on total amount of recharge/withdrawal")
    public MessageResult recharge(TransactionTypeEnum type
            , @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date startDate
            , @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date endDate
    ) {
        return financeStatisticsService.recharge(type, startDate, endDate);
    }
}
