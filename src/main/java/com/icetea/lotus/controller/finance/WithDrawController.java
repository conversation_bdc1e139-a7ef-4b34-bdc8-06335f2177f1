package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Withdraw;
import com.icetea.lotus.model.screen.WithdrawScreen;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedWithdrawService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;

/**
 * Withdrawal management
 */
@Slf4j
@RestController
@RequestMapping("/finance/withdraw")
public class WithDrawController extends BaseAdminController {

    private final ExtendedWithdrawService extendedWithdrawService;

    public WithDrawController(BaseAdminService baseAdminService, ExtendedWithdrawService extendedWithdrawService) {
        super(baseAdminService);
        this.extendedWithdrawService = extendedWithdrawService;
    }

    /**
     * Retrieves the list of currencies available in the withdrawal review.
     *
     * @return MessageResult containing the list of currencies for withdrawal.
     */
    @PreAuthorize("hasRole('finance:withdraw:coin-list')")
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency list in the withdrawal review")
    public MessageResult coinList() {
        return extendedWithdrawService.coinList();
    }

    /**
     * Retrieves the list of currency protocols available in the withdrawal review.
     *
     * @return MessageResult containing the list of currency protocols for withdrawal.
     */
    @PreAuthorize("hasRole('finance:withdraw:protocol-list')")
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency agreement list in the withdrawal review")
    public MessageResult protocolList() {
        return extendedWithdrawService.protocolList();
    }

    /**
     * Retrieves a paginated list of withdrawal review records based on the provided filter criteria.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param withdrawScreen The filter criteria for querying withdrawal records.
     * @param response The HTTP response used for streaming the data.
     * @return MessageResult containing the paginated list of withdrawal records based on the filter criteria.
     * @throws IOException If an error occurs while writing the response.
     */
    @PreAuthorize("hasRole('finance:withdraw:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency withdrawal review list")
    public MessageResult pageQuery(PageModel pageModel, WithdrawScreen withdrawScreen,
                                   HttpServletResponse response) throws IOException {
        return extendedWithdrawService.pageQuery(pageModel, withdrawScreen, response);

    }

    /**
     * Reviews or rejects a withdrawal based on the provided withdraw details.
     *
     * @param withdraw The withdrawal record to be reviewed or rejected.
     * @param bindingResult The result of the validation process.
     * @return MessageResult containing the result of the review/rejection.
     */
    @PreAuthorize("hasRole('finance:withdraw:merge')")
    @PostMapping("/merge")
    @AccessLog(module = AdminModule.FINANCE, operation = "Review/reject withdrawal")
    public MessageResult merge(@Valid Withdraw withdraw, BindingResult bindingResult) {
        return extendedWithdrawService.merge(withdraw, bindingResult);
    }

}
