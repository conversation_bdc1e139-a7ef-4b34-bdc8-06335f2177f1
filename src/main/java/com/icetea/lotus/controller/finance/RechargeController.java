package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedRechargeService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Recharge management
 */
@Slf4j
@RestController
@RequestMapping("/finance/recharge")
public class RechargeController extends BaseAdminController {

    private final ExtendedRechargeService extendedRechargeService;

    public RechargeController(BaseAdminService baseAdminService, ExtendedRechargeService extendedRechargeService) {
        super(baseAdminService);
        this.extendedRechargeService = extendedRechargeService;
    }

    /**
     * Retrieves the list of currencies available in the recharge records.
     *
     * @return MessageResult containing the list of available currencies for recharge.
     */
    @PreAuthorize("hasRole('finance:recharge:coin-list')")
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency list in the recharge record")
    public MessageResult coinList() {
        return extendedRechargeService.coinList();

    }

    /**
     * Retrieves the list of currency protocols associated with recharge records.
     *
     * @return MessageResult containing the list of currency protocols for recharge.
     */
    @PreAuthorize("hasRole('finance:recharge:protocol-list')")
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency protocol list in the recharge record")
    public MessageResult protocolList() {
        return extendedRechargeService.protocolList();

    }

    /**
     * Retrieves a paginated list of recharge records with the given filter criteria.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param depositScreenRequest The filter criteria for querying recharge records (e.g., currency, member ID).
     * @param response The HTTP response used for streaming the data.
     * @return MessageResult containing the paginated list of recharge records based on the filter criteria.
     * @throws IOException If an error occurs while writing the response.
     */
//    @PreAuthorize("hasRole('finance:recharge:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the recharge record")
    public MessageResult pageQuery(PageModel pageModel, DepositScreenRequest depositScreenRequest,
                                   HttpServletResponse response) throws IOException {

        return extendedRechargeService.pageQuery(pageModel, depositScreenRequest, response);
    }

}
