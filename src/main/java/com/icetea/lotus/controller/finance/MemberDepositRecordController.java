package com.icetea.lotus.controller.finance;


import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.model.screen.MemberDepositScreen;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.MemberFinanceService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("finance/member-deposit")
@Slf4j
public class MemberDepositRecordController extends BaseAdminController {

    private final MemberFinanceService memberFinanceService;

    public MemberDepositRecordController(BaseAdminService baseAdminService, MemberFinanceService memberFinanceService) {
        super(baseAdminService);
        this.memberFinanceService = memberFinanceService;
    }

    /**
     * Retrieves a paginated list of coin recharge records based on the provided screen filters.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen The filter criteria for querying coin recharge records (e.g., member ID, recharge amount, status).
     * @return MessageResult containing the paginated list of coin recharge records that match the provided criteria.
     */
    @PreAuthorize("hasRole('finance:member-deposit:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Coin recharge record")
    public MessageResult page(PageModel pageModel, MemberDepositScreen screen) {
        return memberFinanceService.page(pageModel, screen);
    }

    /**
     * Retrieves a paginated list of super deposits for a specific member based on the provided filters.
     * This operation involves a transaction and will roll back in case of an exception.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen The filter criteria for querying super deposit records (e.g., deposit amount, status).
     * @param memberId The ID of the member whose super deposit records are being queried.
     * @return MessageResult containing the paginated list of super deposit records for the given member.
     */
    @PreAuthorize("hasRole('finance:member-deposit:superdeposit-page-query')")
    @PostMapping(value = "/superdeposit-page-query")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult pageSuperDeposit(PageModel pageModel, MemberDepositScreen screen, Long memberId) {
        return memberFinanceService.pageSuperDeposit(pageModel, screen, memberId);
    }
}
