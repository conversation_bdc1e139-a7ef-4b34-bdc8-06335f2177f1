package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.model.screen.ExchangeTradeScreen;
import com.icetea.lotus.service.finance.FinanceStatisticsService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * Coin transaction details
 */

@RestController
@RequestMapping("finance/exchange-transaction")
@RequiredArgsConstructor
public class ExchangeTransactionController extends BaseController {

    private final FinanceStatisticsService financeStatisticsService;

    /**
     * Paginated query for exchange trade details (币币交易明细).
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen The filter criteria for querying exchange trades (e.g., symbol, time range, direction).
     * @return MessageResult containing the paginated list of exchange trade details based on the given criteria.
     */
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "币币交易明细")
    public MessageResult page(PageModel pageModel, ExchangeTradeScreen screen) {
        return financeStatisticsService.page(pageModel, screen);
    }
}
