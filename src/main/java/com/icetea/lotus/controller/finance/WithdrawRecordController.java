package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.model.screen.WithdrawRecordScreen;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedWithdrawService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

@RestController
@Slf4j
@RequestMapping("/finance/withdraw-record")
public class WithdrawRecordController extends BaseAdminController {

    private final ExtendedWithdrawService extendedWithdrawService;

    public WithdrawRecordController(BaseAdminService baseAdminService, ExtendedWithdrawService extendedWithdrawService) {
        super(baseAdminService);
        this.extendedWithdrawService = extendedWithdrawService;
    }

    /**
     * Retrieves all withdrawal records.
     *
     * @return MessageResult containing the list of all withdrawal records.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:all')")
    @GetMapping("/all")
    @AccessLog(module = AdminModule.FINANCE, operation = "All withdrawal records WithdrawRecord")
    public MessageResult all() {
        return extendedWithdrawService.all();
    }

    /**
     * Retrieves a paginated list of withdrawal records based on the provided filter criteria.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen    The filter criteria for querying withdrawal records (e.g., date range, member ID).
     * @return MessageResult containing the paginated list of withdrawal records based on the filter criteria.
     */
    @PreAuthorize("hasAnyRole('finance:withdraw-record:page-query', 'finance:withdraw-record:page-query:success')")
    @RequestMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Pagination query withdrawal record WithdrawRecord")
    public MessageResult pageQuery(
            PageModel pageModel,
            WithdrawRecordScreen screen) {
        return extendedWithdrawService.pageQuery(pageModel, screen);
    }

    /**
     * Retrieves the details of a specific withdrawal record by its ID.
     *
     * @param id The ID of the withdrawal record.
     * @return MessageResult containing the details of the specified withdrawal record.
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('finance:withdraw-record:detail')")
    @AccessLog(module = AdminModule.FINANCE, operation = "WithdrawRecord details")
    public MessageResult detail(@PathVariable("id") Long id) {
        return extendedWithdrawService.detail(id);
    }

    // One-click review passed

    /**
     * Approves a list of withdrawal records in a single operation.
     *
     * @param ids The IDs of the withdrawal records to be approved.
     * @return MessageResult containing the result of the approval operation.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:audit-pass')")
    @PatchMapping("/audit-pass")
    @AccessLog(module = AdminModule.FINANCE, operation = "WithdrawRecord one-click review and approval")
    public MessageResult auditPass(@RequestParam("ids") Long[] ids) {
        return extendedWithdrawService.auditPass(ids);
    }

    // One-click review failed

    /**
     * Rejects a list of withdrawal records in a single operation.
     *
     * @param ids The IDs of the withdrawal records to be rejected.
     * @return MessageResult containing the result of the rejection operation.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:audit-no-pass')")
    @PatchMapping("/audit-no-pass")
    @AccessLog(module = AdminModule.FINANCE, operation = "WithdrawRecord one-click review failed")
    public MessageResult auditNoPass(@RequestParam("ids") Long[] ids) {
        return extendedWithdrawService.auditNoPass(ids);
    }

    /**
     * Adds a transaction flow number to a withdrawal record.
     *
     * @param id                The ID of the withdrawal record.
     * @param transactionNumber The transaction flow number to be added.
     * @return MessageResult containing the result of the operation.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:add-transaction-number')")
    @PatchMapping("/add-transaction-number")
    @AccessLog(module = AdminModule.FINANCE, operation = "Add transaction flow number")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult addNumber(
            @RequestParam("id") Long id,
            @RequestParam("transactionNumber") String transactionNumber) {
        return extendedWithdrawService.addNumber(id, transactionNumber);
    }

    /**
     * Performs batch remittance (bulk payment) for withdrawal records.
     *
     * @param admin             The admin performing the operation.
     * @param ids               The IDs of the withdrawal records to be processed.
     * @param transactionNumber The transaction flow number.
     * @param password          The password used to confirm the remittance.
     * @return MessageResult containing the result of the remittance operation.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:remittance')")
    @PatchMapping("/remittance")
    @AccessLog(module = AdminModule.FINANCE, operation = "Withdrawal record/batch payment in batches")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult remittance(
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
            @RequestParam("ids") Long[] ids,
            @RequestParam("transactionNumber") String transactionNumber,
            @RequestParam("password") String password) {
        return extendedWithdrawService.remittance(admin, ids, transactionNumber, password);
    }

    /**
     * Retrieves a paginated list of withdrawal records for agent-invited users.
     *
     * @param pageModel The pagination and sorting information.
     * @param screen    The filter criteria for querying withdrawal records.
     * @param memberId  The ID of the agent whose withdrawal records are being queried.
     * @return MessageResult containing the paginated list of withdrawal records for the specified agent.
     */
    @PreAuthorize("hasRole('finance:withdraw-record:superwithdraw-page-query')")
    @PostMapping(value = "/superwithdraw-page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Inquiry agent invites users to withdraw cash records WithdrawRecordController")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult pageQuerySuper(
            PageModel pageModel,
            WithdrawRecordScreen screen,
            Long memberId) {
        return extendedWithdrawService.pageQuerySuper(pageModel, screen, memberId);
    }
}
