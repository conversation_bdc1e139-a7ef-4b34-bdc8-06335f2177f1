package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.model.screen.MemberTransactionScreen;
import com.icetea.lotus.model.vo.MemberTransaction2ESVO;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.MemberFinanceService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Date;

@RestController
@Slf4j
@RequestMapping("/finance/member-transaction")
public class MemberTransactionController extends BaseAdminController {

    private final MemberFinanceService memberFinanceService;

    public MemberTransactionController(BaseAdminService baseAdminService, MemberFinanceService memberFinanceService) {
        super(baseAdminService);
        this.memberFinanceService = memberFinanceService;
    }

    /**
     * Retrieves all transaction records for members.
     *
     * @return MessageResult containing all the member transaction records.
     */
    @PreAuthorize("hasRole('finance:member-transaction:all')")
    @PostMapping("/all")
    @AccessLog(module = AdminModule.FINANCE, operation = "All transaction records MemberTransaction")
    public MessageResult all() {
        return memberFinanceService.all();
    }

    /**
     * Retrieves the details of a specific member transaction based on the provided transaction ID.
     *
     * @param id The ID of the transaction to be retrieved.
     * @return MessageResult containing the details of the specified transaction.
     */
    @PreAuthorize("hasRole('finance:member-transaction:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.FINANCE, operation = "Transaction history MemberTransaction details")
    public MessageResult detail(@RequestParam(value = "id") Long id) {
        return memberFinanceService.detail(id);
    }

    /**
     * Retrieves a paginated list of transaction records for members, with various filter options.
     * This method supports filtering by recharge, check, fee, and other criteria.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen    The filter criteria for querying transaction records (e.g., member ID, transaction type).
     * @param response  The HTTP response used for streaming the data.
     * @return MessageResult containing the paginated list of transaction records based on the filter criteria.
     * @throws IOException If an error occurs while writing the response.
     */
    @PreAuthorize("hasAnyRole('finance:member-transaction:page-query', 'finance:member-transaction:page-query:recharge', 'finance:member-transaction:page-query:check', 'finance:member-transaction:page-query:fee')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Pagination Find Transaction Record MemberTransaction")
    public MessageResult pageQuery(
            PageModel pageModel,
            MemberTransactionScreen screen,
            HttpServletResponse response) throws IOException {
        return memberFinanceService.pageQuery(pageModel, screen, response);
    }

    /**
     * Exports the member transaction history to an Excel file based on the provided filter criteria.
     *
     * @param startTime The start date for the transaction period to filter.
     * @param endTime   The end date for the transaction period to filter.
     * @param type      The type of transaction to filter (e.g., deposit, withdrawal, etc.).
     * @param memberId  The ID of the member whose transaction records are being exported.
     * @param request   The HTTP request.
     * @param response  The HTTP response used for exporting the data.
     * @return MessageResult containing the result of the export operation.
     * @throws Exception If an error occurs during the export process.
     */
    @PreAuthorize("hasRole('finance:member-transaction:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.FINANCE, operation = "Export transaction history MemberTransaction Excel")
    public MessageResult outExcel(
            @RequestParam(value = "startTime", required = false) Date startTime,
            @RequestParam(value = "endTime", required = false) Date endTime,
            @RequestParam(value = "type", required = false) TransactionType type,
            @RequestParam(value = "memberId", required = false) Long memberId,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return memberFinanceService.outExcel(startTime, endTime, type, memberId, request, response);
    }


    /**
     * Retrieves a paginated list of transaction records for members using Elasticsearch as the data source.
     *
     * @param transactionVO The value object containing the filter criteria for querying the transaction records.
     * @return MessageResult containing the paginated list of transaction records.
     */
    @PreAuthorize("hasAnyRole('finance:member-transaction:page-query, finance:member-transaction:page-query:recharge, finance:member-transaction:page-query:check, finance:member-transaction:page-query:fee}')")
    @PostMapping("page-query_es")
    @AccessLog(module = AdminModule.FINANCE, operation = "Pagination Find Transaction Record MemberTransaction")
    public MessageResult getPageQueryByES(MemberTransaction2ESVO transactionVO) {
        return memberFinanceService.getPageQueryByES(transactionVO);
    }


    /**
     * Retrieves a paginated list of asset change records for a member below the agent level.
     * This method involves transaction processing and will roll back in case of an exception.
     *
     * @param pageModel The pagination and sorting information (page number, size, etc.).
     * @param screen    The filter criteria for querying asset change records.
     * @param memberId  The ID of the member whose asset change records are being queried.
     * @return MessageResult containing the paginated list of asset change records for the given member.
     */
    @PreAuthorize("hasRole('finance:member-transaction:supertrans-page-query')")
    @PostMapping(value = "/supertrans-page-query")
    @Transactional(rollbackFor = Exception.class)
    @AccessLog(module = AdminModule.FINANCE, operation = "Query the asset change record below the agent MemberTransaction")
    public MessageResult pageQuerySuper(
            PageModel pageModel,
            MemberTransactionScreen screen,
            Long memberId) {
        return memberFinanceService.pageQuerySuper(pageModel, screen, memberId);
    }
}
