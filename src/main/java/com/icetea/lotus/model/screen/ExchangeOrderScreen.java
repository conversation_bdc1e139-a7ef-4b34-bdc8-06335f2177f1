package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExchangeOrderScreen {
    ExchangeOrderType type;
    String coinSymbol;// Coin Unit
    String baseSymbol ;// Settlement unit
    ExchangeOrderStatus status; // TRADING (transaction), COMPLETED (completed), CANCELED (cancel), OVERTIMED (timeout);
    Long memberId;
    // Transaction price
    BigDecimal minPrice ;
    BigDecimal maxPrice ;
    // Trading volume
    BigDecimal minTradeAmount;
    BigDecimal maxTradeAmount;
    // Transaction volume
    BigDecimal minTurnOver;
    BigDecimal maxTurnOver;
    String orderId ;
    ExchangeOrderDirection orderDirection ;
    // Whether to view the robot order 1="Yes" 0="No"
    Integer robotOrder;

    /**
     * 01 (Entrust Order Historical Order)
     */
    BooleanEnum completed ;
    Integer isOut;
}
