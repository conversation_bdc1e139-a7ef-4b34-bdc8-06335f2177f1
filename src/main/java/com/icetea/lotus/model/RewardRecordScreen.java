package com.icetea.lotus.model;

public class RewardRecordScreen {

    private String unit ;

    /**
     * Recommended
     */
    private String presenter ;

    /**
     * Recommended
     */
    private String presentee ;

   /* public List<BooleanExpression> getBooleanExpressions() throws IllegalAccessException {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        Field[] fields =  this.getClass().getDeclaredFields() ;
        for(Field field : fields){
            field.setAccessible(true);
            Object object = field.get(this.getClass());
            if(object!=null){
                booleanExpressions.add()
            }
        }
    }*/
}
