package com.icetea.lotus.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
public class TokenListingDTO {
    private String tokenName;
    private String symbol;
    private Instant listingTime;
    private BigDecimal price;
    private BigDecimal volume24h;
    private String status; // Active / Delisted
    private Boolean spotTradingStatus; // On / Off
    private Boolean futureTradingStatus; // On / Off
}