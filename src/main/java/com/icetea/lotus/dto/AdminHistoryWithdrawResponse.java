package com.icetea.lotus.dto;

import com.icetea.lotus.constant.WithdrawStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminHistoryWithdrawResponse {
    private Long memberId;
    private String username;
    private String email;
    private String coinName;
    private String coinUnit;
    private OffsetDateTime createdAt;
    private BigDecimal amount;
    private WithdrawStatus status;
    private String address;
    private String txId;
    private String network;
    private String transactionNumber;
}
