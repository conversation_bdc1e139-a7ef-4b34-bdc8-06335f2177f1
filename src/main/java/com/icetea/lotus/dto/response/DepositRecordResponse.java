/*
 * Copyright(C) 2025 Icetea Software Company
 * DepositRecordResponse.java, July 28,2025
 * namhm
 */
package com.icetea.lotus.dto.response;

import com.icetea.lotus.constant.WithdrawStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * DTO for returning deposit record details to the client
 * <AUTHOR>
 */
@Data
public class DepositRecordResponse {
    private String id;

    private Long memberId;

    private String coinName;

    private String coinUnit;

    private BigDecimal amount;

    private BigDecimal frozenAmount;

    private Instant createdAt;

    private WithdrawStatus status;

    private String network;

    private String transactionNumber;

    private String txHash;

    private String fromAddress;

    private String fireblockStatus;

    private String username;

    private String email;
}
