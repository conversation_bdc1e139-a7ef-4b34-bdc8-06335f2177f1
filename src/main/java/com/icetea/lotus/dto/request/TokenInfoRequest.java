package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class TokenInfoRequest {
    @NotBlank(message = "The network protocol field is required")
    private String networkProtocol;
    @NotBlank(message = "The contract address field is required")
    private String contractAddress;
    private String name;
    private String symbol;
    private BigDecimal totalSupply;
    private int decimals;
    private String tokenImage;
    private String tokenDescription;
    private BigDecimal circulationSupply;
    private BigDecimal maxSupply;
}