package com.icetea.lotus.config;

import io.minio.MinioClient;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

@Setter
@Getter
@Validated
@Configuration
@Slf4j
@ConfigurationProperties("bitcello.minio")
public class MinioConfig {

    @NotEmpty(message = "MinIO protocol must not be empty")
    private String protocol;

    @NotEmpty(message = "MinIO host must not be empty")
    private String host;

    @NotEmpty(message = "MinIO port must not be empty")
    private String port;

    @NotEmpty(message = "MinIO access key must not be empty")
    private String accessKey;

    @NotEmpty(message = "MinIO secret key must not be empty")
    private String secretKey;

    @NotEmpty(message = "MinIO bucket name must not be empty")
    private String bucketName;

    public String getEndpoint() {
        log.info("Protocol: {}", protocol);
        log.info("Host: {}", host);
        log.info("Port: {}", port);

        var endPoint = String.format("%s://%s:%s", protocol, host, port);
        log.info("MinIO endpoint: {}", endPoint);
        return endPoint;
    }

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(getEndpoint())
                .credentials(accessKey, secretKey)
                .build();
    }
}
