//package com.icetea.lotus.config;
//
//import com.icetea.lotus.service.AdminService;
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import org.jetbrains.annotations.NotNull;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.oauth2.jwt.Jwt;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.OncePerRequestFilter;
//
//import java.io.IOException;
//
//import static com.icetea.lotus.constant.SysConstant.SESSION_ADMIN;
//import static com.icetea.lotus.constant.SysConstant.SESSION_MEMBER;
//
//
///**
// * A Spring filter that extends the {@link OncePerRequestFilter} to ensure the session contains
// * the authenticated user's information during a request. This filter adds a custom user object
// * to the HTTP session if it is not already set.
// * <p>
// * The {@code SessionAttributeFilter} checks whether the current authentication principal
// * is present and validated, then retrieves the corresponding user data from a
// * MemberService implementation. The authenticated user data is subsequently mapped to a
// * custom {@code AuthMember} object and stored in the session under a predefined key.
// * <p>
// * This filter relies on Spring Security's context for authentication details and works
// * to establish a session state based on the available principal token. It ensures that session
// * attributes are accurately synchronized with the user's authentication context.
// * <p>
// * Component registration ensures it is managed within the Spring application context.
// */
//@Component("sessionAttributeFilter")
//public class SessionAttributeFilter extends OncePerRequestFilter {
//
//    public static final String PREFERRED_USERNAME = "preferred_username";
//
//    private final AdminService adminService;
//
//    public SessionAttributeFilter(AdminService adminService) {
//        this.adminService = adminService;
//    }
//
//    @Override
//    protected void doFilterInternal(@NotNull HttpServletRequest request,
//                                    @NotNull HttpServletResponse response,
//                                    @NotNull FilterChain filterChain)
//            throws ServletException, IOException {
//        // Get the current authentication
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//
//
//        if (authentication != null && request.getSession().getAttribute(SESSION_ADMIN) == null) {
//            var username = (String) ((Jwt) authentication.getPrincipal()).getClaims().get(PREFERRED_USERNAME);
//            var admin = adminService.findByUsername(username);
//
//            // Set the AuthMember object in session
//            request.getSession().setAttribute(SESSION_ADMIN, admin);
//        }
//
//        // Continue the filter chain
//        filterChain.doFilter(request, response);
//    }
//}