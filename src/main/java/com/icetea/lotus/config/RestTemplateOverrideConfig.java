package com.icetea.lotus.config;

import com.icetea.lotus.config.oauth2.OAuth2RestTemplateFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateOverrideConfig {
    @Bean
    @Primary
    public OAuth2RestTemplateFactory overriddenOAuth2RestTemplateFactory() {
        return new OAuth2RestTemplateFactory(null, new CexSecurityProperties()) {
            @Override
            public RestTemplate createOAuth2RestTemplate(String clientRegistrationId, String principalName) {
                // Trả về RestTemplate thường hoặc có interceptor custom bearer token
                return new RestTemplate();
            }

            @Override
            public RestTemplate createOAuth2RestTemplate(String principalName) {
                return new RestTemplate();
            }

            @Override
            public RestTemplate createOAuth2RestTemplate() {
                return new RestTemplate();
            }
        };
    }
}
