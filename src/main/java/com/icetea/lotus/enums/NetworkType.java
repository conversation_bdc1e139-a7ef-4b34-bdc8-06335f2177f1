package com.icetea.lotus.enums;

public enum NetworkType {
    ETH("https://ethereum-rpc.publicnode.com"),
    BSC("https://bsc-rpc.publicnode.com");

    private final String rpcUrl;

    NetworkType(String rpcUrl) {
        this.rpcUrl = rpcUrl;
    }

    public String getRpcUrl() {
        return rpcUrl;
    }

    public static NetworkType getNetworkType(String name) {
        for (NetworkType networkType : NetworkType.values()) {
            if (networkType.name().equalsIgnoreCase(name)) {
                return networkType;
            }
        }
        return null;
    }
}
