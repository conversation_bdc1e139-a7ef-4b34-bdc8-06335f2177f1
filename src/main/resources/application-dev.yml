keycloak:
  auth-server-url: ${KEY<PERSON>OAK_AUTH_SERVER_URL:http://************:32082}
  realm: cex-lotus
  resource: cex-admin
  credentials:
    secret: O6xO17JJ7W3M5jjCBNrh6Z84voYLMrUT

spring:
  security:
    jwt:
      secret: "my-super-secret-jwt-key-that-is-at-least-256-bit-long"

#      client:
#        registration:
#          keycloak:
#            client-id: internal-service
#            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
#            authorization-grant-type: client_credentials
#            scope: openid
#        provider:
#          keycloak:
#            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082/realms/cex-lotus}
#            token-uri: ${KEYCLOAK_TOKEN_URI:http://************:32082/realms/cex-lotus/protocol/openid-connect/token}
  main:
    allow-bean-definition-overriding: true

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:3t4sQnnjmABglH4DDTH2m5ANIiDJuigH9VW919pU42knQWGZnJidLBHyjFoRRfYZ}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql
  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:*********************************************************************************************************************************************************************************************
      database: ${SPOT_MONGODB_DATABASE:spot}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      #      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      connect-timeout: ${REDIS_TIMEOUT:30000}
      jedis:
        pool:
          min-idle: 20
          max-idle: 100
          max-wait: 60000
          max-active: 300
    elasticsearch:
      uris: ${ES_URI:localhost}:9200

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: ${CONSUL_DISCOVERY:true}
        service-name: admin
        health-check-path: /admin/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
        fail-fast: true
        heartbeat:
          enabled: true
        deregister: true

    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true

  jackson:
    serialization:
      indent_output: ${SPRING_JACKSON_INDENT_OUTPUT:true}
    date-format: ${SPRING_JACKSON_DATE_FORMAT:yyyy-MM-dd HH:mm:ss}
    time-zone: ${SPRING_JACKSON_TIME_ZONE:GMT+8}

  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:************:30092}
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      batch:
        size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer:
        memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
    consumer:
      enable:
        auto:
          commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      session:
        timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto:
        commit:
          interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:100}
        offset:
          reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      group:
        id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
      concurrency: ${KAFKA_CONSUMER_CONCURRENCY:9}
      maxPollRecordsConfig: ${KAFKA_CONSUMER_MAX_POLL_RECORDS:50}

  devtools:
    restart:
      enabled: ${SPRING_DEVTOOLS_RESTART_ENABLED:true}

  freemarker:
    cache: ${SPRING_FREEMARKER_CACHE:false}
    template-loader-path: ${SPRING_FREEMARKER_TEMPLATE_LOADER_PATH:classpath:/templates}

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

cex-security:
  resource-server-enabled: false
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/system/employee/login"
    - "/system/employee/logout"
    - "/system/employee/refresh-token"
    - "/admin/actuator/health"
    - "/admin/login"
    - "/management/transaction/withdraw"
    - "/management/transaction/withdraw/export"
    - "/finance/transfer/**"

management:
  context-path: /actuator
  health:
    mail:
      enabled: false
    elasticsearch:
      enabled: false
    redis:
      enabled: false
  security:
    enabled: false

endpoints:
  health:
    sensitive: false
  info:
    sensitive: false
  metrics:
    sensitive: false

aliyun:
  accessKeyId: ${ALIYUN_ACCESS_KEY_ID:LTAI5zG6W9gFE32Uw}
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET:D0RFaFT55JSU3223Daqln1T2uWGFP}
  ossEndpoint: ${ALIYUN_OSS_ENDPOINT:oss-cn-shenzhen.aliyuncs.com}
  ossBucketName: ${ALIYUN_OSS_BUCKET_NAME:bizzan}
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

sms:
  driver: ${SMS_DRIVER:diyi}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8CAA321133}
  sign: ${SMS_SIGN:BIZZAN}

commission:
  need:
    real-name: ${COMMISSION_NEED_REAL_NAME:1}
  promotion:
    second-level: ${COMMISSION_PROMOTION_SECOND_LEVEL:1}

spark:
  system:
    md5:
      key: ${SPARK_SYSTEM_MD5_KEY:XehGyeyrVgOV4P8Uf70REVpIw332iVNwNs}
    work-id: ${SPARK_SYSTEM_WORK_ID:1}
    data-center-id: ${SPARK_SYSTEM_DATA_CENTER_ID:1}
    host: ${SPARK_SYSTEM_HOST:smtp.126.com}
    name: ${SPARK_SYSTEM_NAME:BIZZAN}
    admins: ${SPARK_SYSTEM_ADMINS:<EMAIL>}
    admin-phones: ${SPARK_SYSTEM_ADMIN_PHONES:18000000}

access:
  key:
    id: ${ACCESS_KEY_ID:}
    secret: ${ACCESS_KEY_SECRET:}

es:
  client:
    enabled: true

google:
  host: BIZZAN.PRO

oss:
  name: oss

email:
  driver: java

# Add this under the existing configuration
logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.security.web: DEBUG
    org.springframework.security.authentication: DEBUG
    org.springframework.security.access: DEBUG
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

bitcello:
  minio:
    protocol: http
    host: ************
    port: 30000
    access-key: VTp9kKg0QBdn80dCHn0V
    secret-key: Fda7Rfa1Roafn0e9SND57T3T219MXfkTlVQak6fJ
    bucket-name: bitcello-file

#Minio Config
#bitcello:
#  minio:
#    protocol: ${MINIO_PROTOCOL:http}
#    host: ${MINIO_HOST:************}
#    port: ${MINIO_PORT:9000}
#    access-key: ${MINO_ACCESS_KEY:VTp9kKgOQBdn8OdCHn0V}
#    secret-key: ${MINIO_SECRET-KEY:Fda7Rfd1Roafn0e9SND57T2T16MXfkIlVQaK6fJe}
#    bucket-name: ${MINIO_BUCKET_NAME:icetea-software-file}

#Feign client
feign:
  client:
    name: service-wallet
    wallet:
      url: http://************
      #http://localhost:6009